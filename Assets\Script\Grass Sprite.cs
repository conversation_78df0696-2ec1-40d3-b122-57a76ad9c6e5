using UnityEngine;

[RequireComponent(typeof(Terrain))]
public class GrassParticleController : MonoBehaviour
{
    [Header("Grass Settings")]
    public int density = 2000;
    public float grassMinSize = 0.5f;
    public float grassMaxSize = 1.2f;
    public float yOffset = 0.5f;

    [Header("Rendering")]
    public Material grassMaterial;
    public Mesh grassMesh;

    [Header("Effects")]
    public float popoutRate = 0.01f;
    public float popoutDistance = 0.5f;

    private Terrain terrain;
    private TerrainData terrainData;
    private Matrix4x4[] grassMatrices;
    private Vector3[] grassPositions;
    private float[] grassScales;

    void Start()
    {
        terrain = GetComponent<Terrain>();
        terrainData = terrain.terrainData;

        GenerateGrass();
    }

    void GenerateGrass()
    {
        Vector3 terrainSize = terrainData.size;
        grassMatrices = new Matrix4x4[density];
        grassPositions = new Vector3[density];
        grassScales = new float[density];

        Debug.Log($"Generating {density} grass instances on terrain size: {terrainSize}");

        for (int i = 0; i < density; i++)
        {
            float posX = Random.Range(0, terrainSize.x);
            float posZ = Random.Range(0, terrainSize.z);

            Vector3 position = new Vector3(posX, yOffset, posZ) + terrain.transform.position;
            float scale = Random.Range(grassMinSize, grassMaxSize);

            // Store data
            grassPositions[i] = position;
            grassScales[i] = scale;

            // Create initial matrix
            grassMatrices[i] = Matrix4x4.TRS(position, Quaternion.identity, Vector3.one * scale);
        }

        Debug.Log($"Grass generation complete. Material: {grassMaterial != null}, Mesh: {grassMesh != null}");
    }

    void Update()
    {
        // Update billboarding and effects
        if (grassMatrices != null && Camera.main != null)
        {
            UpdateGrassMatrices();
        }

        // Render grass
        RenderGrass();
    }

    void UpdateGrassMatrices()
    {
        Camera camera = Camera.main;
        if (camera == null) return;

        // Get camera's forward direction for orthographic billboarding
        Vector3 cameraForward = camera.transform.forward;
        Vector3 billboardDirection = new Vector3(cameraForward.x, 0, cameraForward.z).normalized;

        if (billboardDirection.magnitude < 0.1f)
        {
            billboardDirection = Vector3.forward;
        }

        // Calculate billboard rotation
        Quaternion billboardRotation = Quaternion.LookRotation(billboardDirection, Vector3.up);
        Vector3 cameraPos = camera.transform.position;

        for (int i = 0; i < grassMatrices.Length; i++)
        {
            Vector3 position = grassPositions[i];
            float baseScale = grassScales[i];

            // Apply popout effect
            float distance = Vector3.Distance(position, cameraPos);
            float scaleFactor = 1.0f;

            if (distance < popoutDistance)
            {
                scaleFactor = 1.0f + (popoutDistance - distance) * popoutRate;
            }

            float finalScale = baseScale * scaleFactor;
            grassMatrices[i] = Matrix4x4.TRS(position, billboardRotation, Vector3.one * finalScale);
        }
    }

    void RenderGrass()
    {
        if (grassMesh != null && grassMaterial != null && grassMatrices != null)
        {
            Graphics.DrawMeshInstanced(
                grassMesh,
                0,
                grassMaterial,
                grassMatrices,
                grassMatrices.Length,
                null,
                UnityEngine.Rendering.ShadowCastingMode.Off,
                false,
                0,
                Camera.main
            );
        }
        else
        {
            if (grassMesh == null) Debug.LogWarning("Grass mesh is null!");
            if (grassMaterial == null) Debug.LogWarning("Grass material is null!");
            if (grassMatrices == null) Debug.LogWarning("Grass matrices is null!");
        }
    }

    // Public method to regenerate grass
    public void RegenerateGrass()
    {
        GenerateGrass();
    }
}
