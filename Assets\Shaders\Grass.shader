Shader "Custom/Grass"
{
    Properties
    {
        _MainTex ("Grass Texture", 2D) = "white" {}
        _AltTex ("Alternative Grass Texture", 2D) = "white" {}
        _BaseColor ("Base Grass Color", Color) = (0.4, 0.7, 0.3, 1)
        _Cutoff ("Alpha Cutoff", Range(0,1)) = 0.5
        _WindStrength ("Wind Strength", Range(0,2)) = 0.5
        _WindSpeed ("Wind Speed", Range(0,5)) = 1.0
        _WindScale ("Wind Scale", Range(0.1,5)) = 1.0

        // Terrain shading properties (matching terrain shader)
        _ShadowColor ("Shadow Color", Color) = (0.4, 0.5, 0.3, 1)
        _HighlightColor ("Highlight Color", Color) = (0.9, 1, 0.8, 1)
        _ShadowThreshold ("Shadow Threshold", Range(0, 1)) = 0.3
        _ColorZones ("Color Zones", Range(2, 12)) = 4

        // Randomization
        _ColorVariationStrength ("Color Variation Strength", Range(0, 1)) = 0.3
        _AltTextureChance ("Alternative Texture Chance", Range(0, 1)) = 0.3
    }

    SubShader
    {
        Tags
        {
            "RenderType"="TransparentCutout"
            "Queue"="AlphaTest"
            "LightMode"="UniversalForward"
        }
        LOD 200
        Cull Off

        Pass
        {
            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_instancing
            #pragma target 3.0

            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"

            struct Attributes
            {
                float4 positionOS : POSITION;
                float2 uv : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv : TEXCOORD0;
                float3 terrainWorldPos : TEXCOORD1;
                float3 terrainNormal : TEXCOORD2;
                float instanceID : TEXCOORD3;
                UNITY_VERTEX_OUTPUT_STEREO
            };

            TEXTURE2D(_MainTex);
            SAMPLER(sampler_MainTex);
            TEXTURE2D(_AltTex);
            SAMPLER(sampler_AltTex);

            CBUFFER_START(UnityPerMaterial)
                float4 _MainTex_ST;
                float4 _AltTex_ST;
                float4 _BaseColor;
                float4 _ShadowColor;
                float4 _HighlightColor;
                float _Cutoff;
                float _WindStrength;
                float _WindSpeed;
                float _WindScale;
                float _ShadowThreshold;
                float _ColorZones;
                float _ColorVariationStrength;
                float _AltTextureChance;
            CBUFFER_END

            // GPU Instancing buffer for terrain data
            UNITY_INSTANCING_BUFFER_START(Props)
                UNITY_DEFINE_INSTANCED_PROP(float3, _TerrainWorldPos)
                UNITY_DEFINE_INSTANCED_PROP(float3, _TerrainNormal)
                UNITY_DEFINE_INSTANCED_PROP(float, _RandomSeed)
            UNITY_INSTANCING_BUFFER_END(Props)

            // Simple noise function
            float SimpleNoise(float2 pos)
            {
                return frac(sin(dot(pos, float2(12.9898, 78.233))) * 43758.5453);
            }

            // Smooth noise for wind and terrain matching
            float SmoothNoise(float2 pos)
            {
                float2 i = floor(pos);
                float2 f = frac(pos);
                f = f * f * (3.0 - 2.0 * f);

                float a = SimpleNoise(i);
                float b = SimpleNoise(i + float2(1.0, 0.0));
                float c = SimpleNoise(i + float2(0.0, 1.0));
                float d = SimpleNoise(i + float2(1.0, 1.0));

                return lerp(lerp(a, b, f.x), lerp(c, d, f.x), f.y);
            }

            // Posterize function matching terrain shader
            float3 PosterizeColor(float3 color, float zones)
            {
                return floor(color * zones) / zones;
            }

            // Generate terrain-matching color variation
            float3 GetTerrainColorVariation(float3 worldPos, float randomSeed)
            {
                // Use same noise pattern as terrain for consistent coloring
                float2 noiseUV = worldPos.xz * 2.0; // Match terrain noise scale
                float noise1 = SmoothNoise(noiseUV);
                float noise2 = SmoothNoise(noiseUV * 2.3 + float2(1.7, 9.2));
                float noise3 = SmoothNoise(noiseUV * 0.5 + float2(8.3, 2.8));

                float combinedNoise = (noise1 * 0.5 + noise2 * 0.3 + noise3 * 0.2);
                float colorZone = floor(combinedNoise * _ColorZones) / _ColorZones;

                // Create same color variations as terrain
                float3 darkVariation = _BaseColor.rgb * 0.7;
                float3 lightVariation = _BaseColor.rgb * 1.3;
                float3 midVariation = _BaseColor.rgb * 0.9;

                float3 terrainColor = _BaseColor.rgb;
                if (colorZone < 0.25)
                    terrainColor = darkVariation;
                else if (colorZone < 0.5)
                    terrainColor = midVariation;
                else if (colorZone < 0.75)
                    terrainColor = _BaseColor.rgb;
                else
                    terrainColor = lightVariation;

                // Add per-instance random variation
                float3 randomVariation = lerp(float3(0.8, 0.8, 0.8), float3(1.2, 1.2, 1.2), randomSeed);

                return terrainColor * randomVariation;
            }

            Varyings vert(Attributes input)
            {
                Varyings output = (Varyings)0;

                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);

                // Get instanced terrain data
                float3 terrainWorldPos = UNITY_ACCESS_INSTANCED_PROP(Props, _TerrainWorldPos);
                float3 terrainNormal = UNITY_ACCESS_INSTANCED_PROP(Props, _TerrainNormal);
                float randomSeed = UNITY_ACCESS_INSTANCED_PROP(Props, _RandomSeed);

                // Calculate world position for wind
                VertexPositionInputs vertexInput = GetVertexPositionInputs(input.positionOS.xyz);
                float3 worldPos = vertexInput.positionWS;

                // Wind animation using noise
                float time = _Time.y * _WindSpeed;
                float2 windPos = worldPos.xz * _WindScale + time * 0.1;

                // Generate wind noise
                float windNoise1 = SmoothNoise(windPos) * 2.0 - 1.0;
                float windNoise2 = SmoothNoise(windPos * 2.0 + 1.7) * 2.0 - 1.0;
                float windNoise3 = SmoothNoise(windPos * 4.0 + 3.4) * 2.0 - 1.0;

                // Combine different frequencies of noise
                float windEffect = (windNoise1 + windNoise2 * 0.5 + windNoise3 * 0.25) / 1.75;

                // Apply wind only to top vertices (based on UV.y)
                float windMask = input.uv.y;
                windEffect *= windMask * _WindStrength;

                // Apply wind displacement to object space
                input.positionOS.x += windEffect;
                input.positionOS.z += windEffect * 0.3;

                // Recalculate position after wind
                vertexInput = GetVertexPositionInputs(input.positionOS.xyz);

                output.positionCS = vertexInput.positionCS;
                output.uv = TRANSFORM_TEX(input.uv, _MainTex);

                // Pass terrain data for shading
                output.terrainWorldPos = terrainWorldPos;
                output.terrainNormal = terrainNormal;
                output.instanceID = randomSeed;

                return output;
            }

            half4 frag(Varyings input) : SV_Target
            {
                // Determine which texture to use based on random seed
                bool useAltTexture = input.instanceID > (1.0 - _AltTextureChance);

                half4 grassTexture;
                if (useAltTexture)
                {
                    grassTexture = SAMPLE_TEXTURE2D(_AltTex, sampler_AltTex, input.uv);
                }
                else
                {
                    grassTexture = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, input.uv);
                }

                // Alpha clipping
                clip(grassTexture.a - _Cutoff);

                // Get terrain-matching color variation
                float3 terrainColor = GetTerrainColorVariation(input.terrainWorldPos, input.instanceID);

                // Calculate lighting using terrain normal (same as terrain)
                Light mainLight = GetMainLight();
                float3 normalWS = normalize(input.terrainNormal);
                float NdotL = dot(normalWS, mainLight.direction);

                // Create flat toon lighting with hard boundaries (matching terrain)
                float lightStep = step(_ShadowThreshold, NdotL);

                // Apply flat lighting - either full light or shadow, no gradients
                half3 lightColor = lerp(_ShadowColor.rgb, _HighlightColor.rgb, lightStep);

                // Combine grass texture with terrain-based color and lighting
                half3 baseColor = grassTexture.rgb * terrainColor * lightColor;

                // Posterize for flat color zones (matching terrain)
                half3 finalColor = PosterizeColor(baseColor, _ColorZones);

                return half4(finalColor, grassTexture.a);
            }
            ENDHLSL
        }
    }

    FallBack "Hidden/Universal Render Pipeline/FallbackError"
}
