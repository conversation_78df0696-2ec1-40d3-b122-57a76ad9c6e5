Shader "Custom/Grass"
{
    Properties
    {
        _MainTex ("Grass Texture", 2D) = "white" {}
        _Color ("Tint Color", Color) = (1,1,1,1)
        _Cutoff ("Alpha Cutoff", Range(0,1)) = 0.5
        _WindStrength ("Wind Strength", Range(0,2)) = 0.5
        _WindSpeed ("Wind Speed", Range(0,5)) = 1.0
        _WindScale ("Wind Scale", Range(0.1,5)) = 1.0
    }

    SubShader
    {
        Tags
        {
            "RenderType"="TransparentCutout"
            "Queue"="AlphaTest"
        }
        LOD 200
        Cull Off

        Pass
        {
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_instancing
            #pragma target 3.0

            #include "UnityCG.cginc"

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct v2f
            {
                float4 pos : SV_POSITION;
                float2 uv : TEXCOORD0;
                UNITY_VERTEX_OUTPUT_STEREO
            };

            sampler2D _MainTex;
            float4 _MainTex_ST;
            fixed4 _Color;
            fixed _Cutoff;
            float _WindStrength;
            float _WindSpeed;
            float _WindScale;

            UNITY_INSTANCING_BUFFER_START(Props)
            UNITY_INSTANCING_BUFFER_END(Props)

            // Simple noise function
            float noise(float2 pos)
            {
                return frac(sin(dot(pos, float2(12.9898, 78.233))) * 43758.5453);
            }

            // Smooth noise
            float smoothNoise(float2 pos)
            {
                float2 i = floor(pos);
                float2 f = frac(pos);
                f = f * f * (3.0 - 2.0 * f);

                float a = noise(i);
                float b = noise(i + float2(1.0, 0.0));
                float c = noise(i + float2(0.0, 1.0));
                float d = noise(i + float2(1.0, 1.0));

                return lerp(lerp(a, b, f.x), lerp(c, d, f.x), f.y);
            }

            v2f vert(appdata v)
            {
                v2f o;

                UNITY_SETUP_INSTANCE_ID(v);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(o);

                // Calculate world position for wind
                float4 worldPos = mul(unity_ObjectToWorld, v.vertex);

                // Wind animation using noise
                float time = _Time.y * _WindSpeed;
                float2 windPos = worldPos.xz * _WindScale + time * 0.1;

                // Generate wind noise
                float windNoise1 = smoothNoise(windPos) * 2.0 - 1.0;
                float windNoise2 = smoothNoise(windPos * 2.0 + 1.7) * 2.0 - 1.0;
                float windNoise3 = smoothNoise(windPos * 4.0 + 3.4) * 2.0 - 1.0;

                // Combine different frequencies of noise
                float windEffect = (windNoise1 + windNoise2 * 0.5 + windNoise3 * 0.25) / 1.75;

                // Apply wind only to top vertices (based on UV.y)
                float windMask = v.uv.y;
                windEffect *= windMask * _WindStrength;

                // Apply wind displacement
                v.vertex.x += windEffect;
                v.vertex.z += windEffect * 0.3; // Less movement in Z direction

                o.pos = UnityObjectToClipPos(v.vertex);
                o.uv = TRANSFORM_TEX(v.uv, _MainTex);

                return o;
            }

            fixed4 frag(v2f i) : SV_Target
            {
                fixed4 col = tex2D(_MainTex, i.uv) * _Color;
                clip(col.a - _Cutoff);
                return col;
            }
            ENDCG
        }
    }

    FallBack "Transparent/Cutout/VertexLit"
}
