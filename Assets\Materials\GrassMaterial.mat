%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: GrassMaterial
  m_Shader: {fileID: 4800000, guid: 10ffd97280169d24f9ede9247812114f, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _AltTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _Cutoff: 0.5
    - _WindStrength: 0.5
    - _WindSpeed: 1.0
    - _WindScale: 1.0
    - _ShadowThreshold: 0.3
    - _ColorZones: 4
    - _ColorVariationStrength: 0.3
    - _AltTextureChance: 0.3
    m_Colors:
    - _BaseColor: {r: 0.4, g: 0.7, b: 0.3, a: 1}
    - _ShadowColor: {r: 0.4, g: 0.5, b: 0.3, a: 1}
    - _HighlightColor: {r: 0.9, g: 1, b: 0.8, a: 1}
  m_BuildTextureStacks: []
