Using pre-set license
Built from '6000.0/staging' branch; Version is '6000.0.34f1 (5ab2d9ed9190) revision 5944025'; Using compiler version '193933523'; Build Type 'Release'
OS: 'Windows 11  (10.0.22631) 64bit CoreSingleLanguage' Language: 'en' Physical Memory: 16251 MB
BatchMode: 1, IsHumanControllingUs: 0, StartBugReporterOnCrash: 0, Is64bit: 1, IsPro: 1

COMMAND LINE ARGUMENTS:
E:\Unity\Editior\6000.0.34f1\Editor\Unity.exe
-adb2
-batchMode
-noUpm
-name
AssetImportWorker0
-projectPath
E:/Unity/Projects/MMXXV
-logFile
Logs/AssetImportWorker0.log
-srvPort
62053
-job-worker-count
3
-background-job-worker-count
8
-gc-helper-count
1
Successfully changed project path to: E:/Unity/Projects/MMXXV
E:/Unity/Projects/MMXXV
[UnityMemory] Configuration Parameters - Can be set up in boot.config
    "memorysetup-bucket-allocator-granularity=16"
    "memorysetup-bucket-allocator-bucket-count=8"
    "memorysetup-bucket-allocator-block-size=33554432"
    "memorysetup-bucket-allocator-block-count=8"
    "memorysetup-main-allocator-block-size=16777216"
    "memorysetup-thread-allocator-block-size=16777216"
    "memorysetup-gfx-main-allocator-block-size=16777216"
    "memorysetup-gfx-thread-allocator-block-size=16777216"
    "memorysetup-cache-allocator-block-size=4194304"
    "memorysetup-typetree-allocator-block-size=2097152"
    "memorysetup-profiler-bucket-allocator-granularity=16"
    "memorysetup-profiler-bucket-allocator-bucket-count=8"
    "memorysetup-profiler-bucket-allocator-block-size=33554432"
    "memorysetup-profiler-bucket-allocator-block-count=8"
    "memorysetup-profiler-allocator-block-size=16777216"
    "memorysetup-profiler-editor-allocator-block-size=1048576"
    "memorysetup-temp-allocator-size-main=16777216"
    "memorysetup-job-temp-allocator-block-size=2097152"
    "memorysetup-job-temp-allocator-block-size-background=1048576"
    "memorysetup-job-temp-allocator-reduction-small-platforms=262144"
    "memorysetup-allocator-temp-initial-block-size-main=262144"
    "memorysetup-allocator-temp-initial-block-size-worker=262144"
    "memorysetup-temp-allocator-size-background-worker=32768"
    "memorysetup-temp-allocator-size-job-worker=262144"
    "memorysetup-temp-allocator-size-preload-manager=33554432"
    "memorysetup-temp-allocator-size-nav-mesh-worker=65536"
    "memorysetup-temp-allocator-size-audio-worker=65536"
    "memorysetup-temp-allocator-size-cloud-worker=32768"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gi-baking-worker=262144"
    "memorysetup-temp-allocator-size-gfx=262144"
Player connection [22596]  Target information:

Player connection [22596]  * "[IP] *********** [Port] 0 [Flags] 2 [Guid] 515126427 [EditorId] 515126427 [Version] 1048832 [Id] WindowsEditor(7,Atichat) [Debug] 1 [PackageName] WindowsEditor [ProjectName] Editor" 

Player connection [22596] Host joined multi-casting on [***********:54997]...
Player connection [22596] Host joined alternative multi-casting on [***********:34997]...
JobSystem: Creating JobQueue using job-worker-count value 3
Input System module state changed to: Initialized.
[Physics::Module] Initialized fallback backend.
[Physics::Module] Id: 0xdecafbad
Library Redirect Path: Library/
[Physics::Module] Selected backend.
[Physics::Module] Name: PhysX
[Physics::Module] Id: 0xf2b8ea05
[Physics::Module] SDK Version: 4.1.2
[Physics::Module] Integration Version: 1.0.0
[Physics::Module] Threading Mode: Multi-Threaded
Refreshing native plugins compatible for Editor in 5.83 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Initialize engine version: 6000.0.34f1 (5ab2d9ed9190)
[Subsystems] Discovering subsystems at path E:/Unity/Editior/6000.0.34f1/Editor/Data/Resources/UnitySubsystems
[Subsystems] Discovering subsystems at path E:/Unity/Projects/MMXXV/Assets
GfxDevice: creating device client; kGfxThreadingModeNonThreaded
Direct3D:
    Version:  Direct3D 11.0 [level 11.1]
    Renderer: NVIDIA GeForce GTX 1650 Ti (ID=0x1f95)
    Vendor:   NVIDIA
    VRAM:     3935 MB
    Driver:   32.0.15.7680
Initialize mono
Mono path[0] = 'E:/Unity/Editior/6000.0.34f1/Editor/Data/Managed'
Mono path[1] = 'E:/Unity/Editior/6000.0.34f1/Editor/Data/MonoBleedingEdge/lib/mono/unityjit-win32'
Mono config path = 'E:/Unity/Editior/6000.0.34f1/Editor/Data/MonoBleedingEdge/etc'
Using monoOptions --debugger-agent=transport=dt_socket,embedding=1,server=y,suspend=n,address=127.0.0.1:56376
Begin MonoManager ReloadAssembly
Registering precompiled unity dll's ...
Register platform support module: E:/Unity/Editior/6000.0.34f1/Editor/Data/PlaybackEngines/WebGLSupport/UnityEditor.WebGL.Extensions.dll
Register platform support module: E:/Unity/Editior/6000.0.34f1/Editor/Data/PlaybackEngines/WindowsStandaloneSupport/UnityEditor.WindowsStandalone.Extensions.dll
Registered in 0.004180 seconds.
- Loaded All Assemblies, in  0.585 seconds
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.477 seconds
Domain Reload Profiling: 1061ms
	BeginReloadAssembly (211ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (0ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (1ms)
	RebuildCommonClasses (60ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (63ms)
	LoadAllAssembliesAndSetupDomain (234ms)
		LoadAssemblies (211ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (229ms)
			TypeCache.Refresh (228ms)
				TypeCache.ScanAssembly (210ms)
			BuildScriptInfoCaches (0ms)
			ResolveRequiredComponents (0ms)
	FinalizeReload (478ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (413ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (29ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (74ms)
			ProcessInitializeOnLoadAttributes (228ms)
			ProcessInitializeOnLoadMethodAttributes (77ms)
			AfterProcessingInitializeOnLoad (0ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (0ms)
========================================================================
Worker process is ready to serve import requests
Import Worker Mode flag is 0x00
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.093 seconds
Refreshing native plugins compatible for Editor in 1.65 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.099 seconds
Domain Reload Profiling: 2192ms
	BeginReloadAssembly (264ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (37ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (49ms)
	LoadAllAssembliesAndSetupDomain (712ms)
		LoadAssemblies (514ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (368ms)
			TypeCache.Refresh (262ms)
				TypeCache.ScanAssembly (240ms)
			BuildScriptInfoCaches (87ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1100ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (860ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (148ms)
			ProcessInitializeOnLoadAttributes (564ms)
			ProcessInitializeOnLoadMethodAttributes (133ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (12ms)
Launched and connected shader compiler UnityShaderCompiler.exe after 0.02 seconds
Refreshing native plugins compatible for Editor in 3.01 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 244 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6189 unused Assets / (8.6 MB). Loaded Objects now: 6885.
Memory consumption went from 184.9 MB to 176.2 MB.
Total: 19.420400 ms (FindLiveObjects: 1.027000 ms CreateObjectMapping: 2.096600 ms MarkObjects: 9.128500 ms  DeleteObjects: 7.166800 ms)

========================================================================
Received Import Request.
  Time since last request: 244107.555641 seconds.
  path: Assets/Shaders/Grass.shader
  artifactKey: Guid(10ffd97280169d24f9ede9247812114f) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Shaders/Grass.shader using Guid(10ffd97280169d24f9ede9247812114f) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'ad51502ee63abd56eb11a98267128dff') in 0.0508587 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 0

========================================================================
Received Import Request.
  Time since last request: 24.239659 seconds.
  path: Assets/Shaders/Grass1.shader
  artifactKey: Guid(bdf32e93fc49ade49b4374e8a359ec31) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Shaders/Grass1.shader using Guid(bdf32e93fc49ade49b4374e8a359ec31) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '36820fd37ebf9033062bb3dff3322aba') in 0.0030539 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 1

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.940 seconds
Refreshing native plugins compatible for Editor in 1.93 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.639 seconds
Domain Reload Profiling: 2582ms
	BeginReloadAssembly (286ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (67ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (15ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (564ms)
		LoadAssemblies (469ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (262ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (228ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1640ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1281ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (133ms)
			ProcessInitializeOnLoadAttributes (942ms)
			ProcessInitializeOnLoadMethodAttributes (194ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (24ms)
Refreshing native plugins compatible for Editor in 4.12 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 58 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (9.3 MB). Loaded Objects now: 6904.
Memory consumption went from 166.4 MB to 157.0 MB.
Total: 24.882300 ms (FindLiveObjects: 1.598200 ms CreateObjectMapping: 2.447100 ms MarkObjects: 10.357700 ms  DeleteObjects: 10.477600 ms)

Prepare: number of updated asset objects reloaded= 1
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.898 seconds
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.156 seconds
Domain Reload Profiling: 2057ms
	BeginReloadAssembly (261ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (64ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (551ms)
		LoadAssemblies (430ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (268ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (242ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1157ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (900ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (142ms)
			ProcessInitializeOnLoadAttributes (650ms)
			ProcessInitializeOnLoadMethodAttributes (99ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 2.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 49 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.7 MB). Loaded Objects now: 6907.
Memory consumption went from 165.1 MB to 156.4 MB.
Total: 21.378500 ms (FindLiveObjects: 1.258700 ms CreateObjectMapping: 2.511600 ms MarkObjects: 9.589400 ms  DeleteObjects: 8.017300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.892 seconds
Refreshing native plugins compatible for Editor in 1.34 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.125 seconds
Domain Reload Profiling: 2021ms
	BeginReloadAssembly (262ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (534ms)
		LoadAssemblies (427ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (266ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (243ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1125ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (851ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (126ms)
			ProcessInitializeOnLoadAttributes (621ms)
			ProcessInitializeOnLoadMethodAttributes (94ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 2.70 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.7 MB). Loaded Objects now: 6909.
Memory consumption went from 164.6 MB to 155.9 MB.
Total: 22.721400 ms (FindLiveObjects: 1.405400 ms CreateObjectMapping: 2.411900 ms MarkObjects: 10.289600 ms  DeleteObjects: 8.612700 ms)

Prepare: number of updated asset objects reloaded= 1
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.042 seconds
Refreshing native plugins compatible for Editor in 1.64 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.337 seconds
Domain Reload Profiling: 2381ms
	BeginReloadAssembly (308ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (60ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (630ms)
		LoadAssemblies (536ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (282ms)
			TypeCache.Refresh (12ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (251ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1338ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1020ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (7ms)
			BeforeProcessingInitializeOnLoad (163ms)
			ProcessInitializeOnLoadAttributes (732ms)
			ProcessInitializeOnLoadMethodAttributes (109ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (24ms)
Refreshing native plugins compatible for Editor in 5.08 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.5 MB). Loaded Objects now: 6911.
Memory consumption went from 164.5 MB to 156.0 MB.
Total: 110.176400 ms (FindLiveObjects: 2.267900 ms CreateObjectMapping: 75.258300 ms MarkObjects: 23.097500 ms  DeleteObjects: 9.551200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.857 seconds
Refreshing native plugins compatible for Editor in 1.43 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.115 seconds
Domain Reload Profiling: 1977ms
	BeginReloadAssembly (252ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (6ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (520ms)
		LoadAssemblies (423ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (247ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (223ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1116ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (831ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (132ms)
			ProcessInitializeOnLoadAttributes (597ms)
			ProcessInitializeOnLoadMethodAttributes (90ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 2.54 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.8 MB). Loaded Objects now: 6913.
Memory consumption went from 164.5 MB to 155.6 MB.
Total: 21.104000 ms (FindLiveObjects: 1.132700 ms CreateObjectMapping: 2.498600 ms MarkObjects: 8.612700 ms  DeleteObjects: 8.857900 ms)

Prepare: number of updated asset objects reloaded= 1
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.950 seconds
Refreshing native plugins compatible for Editor in 1.81 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.177 seconds
Domain Reload Profiling: 2129ms
	BeginReloadAssembly (306ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (93ms)
	RebuildCommonClasses (69ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (525ms)
		LoadAssemblies (446ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (238ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (211ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1178ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (721ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (129ms)
			ProcessInitializeOnLoadAttributes (512ms)
			ProcessInitializeOnLoadMethodAttributes (71ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.22 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.3 MB). Loaded Objects now: 6915.
Memory consumption went from 164.5 MB to 156.2 MB.
Total: 18.868900 ms (FindLiveObjects: 1.216900 ms CreateObjectMapping: 2.160700 ms MarkObjects: 8.591900 ms  DeleteObjects: 6.897500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.112 seconds
Refreshing native plugins compatible for Editor in 1.65 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.103 seconds
Domain Reload Profiling: 2217ms
	BeginReloadAssembly (325ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (78ms)
	RebuildCommonClasses (56ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (680ms)
		LoadAssemblies (490ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (383ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (353ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1103ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (773ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (129ms)
			ProcessInitializeOnLoadAttributes (558ms)
			ProcessInitializeOnLoadMethodAttributes (74ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 2.46 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (9.1 MB). Loaded Objects now: 6917.
Memory consumption went from 164.5 MB to 155.4 MB.
Total: 24.023500 ms (FindLiveObjects: 1.370600 ms CreateObjectMapping: 2.066600 ms MarkObjects: 10.695800 ms  DeleteObjects: 9.888500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.064 seconds
Refreshing native plugins compatible for Editor in 1.95 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.344 seconds
Domain Reload Profiling: 2413ms
	BeginReloadAssembly (342ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (109ms)
	RebuildCommonClasses (51ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (622ms)
		LoadAssemblies (499ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (292ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (261ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1345ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (973ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (208ms)
			ProcessInitializeOnLoadAttributes (639ms)
			ProcessInitializeOnLoadMethodAttributes (111ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 2.88 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.7 MB). Loaded Objects now: 6919.
Memory consumption went from 164.5 MB to 155.8 MB.
Total: 24.442600 ms (FindLiveObjects: 1.499700 ms CreateObjectMapping: 2.169000 ms MarkObjects: 11.547000 ms  DeleteObjects: 9.225300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.198 seconds
Refreshing native plugins compatible for Editor in 1.37 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.242 seconds
Domain Reload Profiling: 2444ms
	BeginReloadAssembly (335ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (13ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (62ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (754ms)
		LoadAssemblies (662ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (292ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (259ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1243ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (956ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (145ms)
			ProcessInitializeOnLoadAttributes (679ms)
			ProcessInitializeOnLoadMethodAttributes (122ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 4.40 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.6 MB). Loaded Objects now: 6921.
Memory consumption went from 164.5 MB to 155.9 MB.
Total: 23.076400 ms (FindLiveObjects: 1.227800 ms CreateObjectMapping: 1.978600 ms MarkObjects: 10.278800 ms  DeleteObjects: 9.588000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.975 seconds
Refreshing native plugins compatible for Editor in 1.27 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.141 seconds
Domain Reload Profiling: 2120ms
	BeginReloadAssembly (287ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (60ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (583ms)
		LoadAssemblies (499ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (255ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (230ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1143ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (849ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (149ms)
			ProcessInitializeOnLoadAttributes (585ms)
			ProcessInitializeOnLoadMethodAttributes (104ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 3.60 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.5 MB). Loaded Objects now: 6923.
Memory consumption went from 164.5 MB to 156.0 MB.
Total: 29.302500 ms (FindLiveObjects: 1.516000 ms CreateObjectMapping: 2.326200 ms MarkObjects: 14.726200 ms  DeleteObjects: 10.732900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.157 seconds
Refreshing native plugins compatible for Editor in 1.97 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.393 seconds
Domain Reload Profiling: 2551ms
	BeginReloadAssembly (321ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (69ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (718ms)
		LoadAssemblies (590ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (321ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (284ms)
			ResolveRequiredComponents (19ms)
	FinalizeReload (1394ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1062ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (8ms)
			BeforeProcessingInitializeOnLoad (169ms)
			ProcessInitializeOnLoadAttributes (731ms)
			ProcessInitializeOnLoadMethodAttributes (146ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (29ms)
Refreshing native plugins compatible for Editor in 4.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (9.2 MB). Loaded Objects now: 6925.
Memory consumption went from 164.5 MB to 155.3 MB.
Total: 32.557200 ms (FindLiveObjects: 1.852300 ms CreateObjectMapping: 2.248400 ms MarkObjects: 14.663300 ms  DeleteObjects: 13.791300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.108 seconds
Refreshing native plugins compatible for Editor in 1.73 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.326 seconds
Domain Reload Profiling: 2438ms
	BeginReloadAssembly (315ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (66ms)
	RebuildCommonClasses (56ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (42ms)
	LoadAllAssembliesAndSetupDomain (677ms)
		LoadAssemblies (558ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (305ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (274ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1327ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1010ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (179ms)
			ProcessInitializeOnLoadAttributes (698ms)
			ProcessInitializeOnLoadMethodAttributes (119ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 3.58 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.5 MB). Loaded Objects now: 6927.
Memory consumption went from 164.5 MB to 156.0 MB.
Total: 23.744300 ms (FindLiveObjects: 1.322200 ms CreateObjectMapping: 2.412800 ms MarkObjects: 10.792200 ms  DeleteObjects: 9.215600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 2505.501754 seconds.
  path: Assets/Texture/grass.png
  artifactKey: Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Texture/grass.png using Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '0eb47b37d6f381a736d7be70caba5172') in 0.3181995 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 56.871412 seconds.
  path: Assets/Texture/grass.png
  artifactKey: Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Texture/grass.png using Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7e4be46ace16c86e54da7269ac74341b') in 0.0211324 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 42.879234 seconds.
  path: Assets/New Terrain 3.asset
  artifactKey: Guid(fb6ed83b19653c14cb957012fe782cb0) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/New Terrain 3.asset using Guid(fb6ed83b19653c14cb957012fe782cb0) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'e3eaa6cefa8844975319cdb82f240c67') in 0.0290946 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 9

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.980 seconds
Refreshing native plugins compatible for Editor in 1.85 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.128 seconds
Domain Reload Profiling: 2113ms
	BeginReloadAssembly (287ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (71ms)
	RebuildCommonClasses (54ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (584ms)
		LoadAssemblies (464ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (281ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (250ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1129ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (848ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (142ms)
			ProcessInitializeOnLoadAttributes (594ms)
			ProcessInitializeOnLoadMethodAttributes (102ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 3.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.4 MB). Loaded Objects now: 6931.
Memory consumption went from 164.6 MB to 156.1 MB.
Total: 21.658700 ms (FindLiveObjects: 1.307400 ms CreateObjectMapping: 2.027300 ms MarkObjects: 9.669700 ms  DeleteObjects: 8.652300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 70.154133 seconds.
  path: Assets/Texture/grass.png
  artifactKey: Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Texture/grass.png using Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '4600a4431b8c815f9b6a29886c3d1a89') in 0.0864504 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 15.142117 seconds.
  path: Assets/Texture/grass.png
  artifactKey: Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Texture/grass.png using Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '8b0a28a8e931d8e02aab3e82f855481d') in 0.0203653 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.925 seconds
Refreshing native plugins compatible for Editor in 1.27 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.178 seconds
Domain Reload Profiling: 2104ms
	BeginReloadAssembly (277ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (63ms)
	RebuildCommonClasses (50ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (553ms)
		LoadAssemblies (444ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (273ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (244ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1178ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (901ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (157ms)
			ProcessInitializeOnLoadAttributes (631ms)
			ProcessInitializeOnLoadMethodAttributes (104ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 2.16 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.7 MB). Loaded Objects now: 6933.
Memory consumption went from 164.6 MB to 155.8 MB.
Total: 26.508500 ms (FindLiveObjects: 1.804800 ms CreateObjectMapping: 3.315500 ms MarkObjects: 11.368600 ms  DeleteObjects: 10.018600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 18.844725 seconds.
  path: Assets/Texture/grass.png
  artifactKey: Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Texture/grass.png using Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'f58ff01b2b4c7368cc5714675567ca42') in 0.0772326 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.087 seconds
Refreshing native plugins compatible for Editor in 1.77 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.386 seconds
Domain Reload Profiling: 2478ms
	BeginReloadAssembly (321ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (72ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (23ms)
	initialDomainReloadingComplete (40ms)
	LoadAllAssembliesAndSetupDomain (649ms)
		LoadAssemblies (523ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (307ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (276ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1387ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1072ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (170ms)
			ProcessInitializeOnLoadAttributes (773ms)
			ProcessInitializeOnLoadMethodAttributes (117ms)
			AfterProcessingInitializeOnLoad (4ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (30ms)
Refreshing native plugins compatible for Editor in 2.46 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.1 MB). Loaded Objects now: 6935.
Memory consumption went from 164.6 MB to 156.5 MB.
Total: 18.621700 ms (FindLiveObjects: 1.147000 ms CreateObjectMapping: 1.932400 ms MarkObjects: 8.331100 ms  DeleteObjects: 7.209300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 55.301011 seconds.
  path: Assets/Texture/grass.png
  artifactKey: Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Texture/grass.png using Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'd71d70795981a67393f0f437dbac2d61') in 0.0719135 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 3.884712 seconds.
  path: Assets/Texture/grass.png
  artifactKey: Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Texture/grass.png using Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'bf222034dddf2970fca22149bca265cd') in 0.0218858 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 5.110492 seconds.
  path: Assets/Texture/grass.png
  artifactKey: Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Texture/grass.png using Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'a4b01a326b08e1aeb643502fa4133b85') in 0.0183759 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 6.596930 seconds.
  path: Assets/Texture/grass.png
  artifactKey: Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Texture/grass.png using Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '190ffa693f2b0efd9a7667ea699eee57') in 0.0153491 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.049 seconds
Refreshing native plugins compatible for Editor in 1.26 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.527 seconds
Domain Reload Profiling: 2579ms
	BeginReloadAssembly (296ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (71ms)
	RebuildCommonClasses (58ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (642ms)
		LoadAssemblies (504ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (304ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (275ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1528ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1237ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (173ms)
			ProcessInitializeOnLoadAttributes (933ms)
			ProcessInitializeOnLoadMethodAttributes (120ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 5.54 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.6 MB). Loaded Objects now: 6937.
Memory consumption went from 164.6 MB to 156.0 MB.
Total: 27.030400 ms (FindLiveObjects: 4.250900 ms CreateObjectMapping: 2.730600 ms MarkObjects: 10.523200 ms  DeleteObjects: 9.524200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Import Request.
  Time since last request: 81.853602 seconds.
  path: Assets/Texture/GrassAlpha.png
  artifactKey: Guid(45deaba19dbd4944294954da14516e97) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Texture/GrassAlpha.png using Guid(45deaba19dbd4944294954da14516e97) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: 'fa95264549a434a30d879bb28432e282') in 0.1126698 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 6.997652 seconds.
  path: Assets/Texture/GrassAlpha.png
  artifactKey: Guid(45deaba19dbd4944294954da14516e97) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Texture/GrassAlpha.png using Guid(45deaba19dbd4944294954da14516e97) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '894d25e321e87e1ffdf27f3e55348583') in 0.0202445 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 1369.053860 seconds.
  path: Assets/Texture/grass.png
  artifactKey: Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Texture/grass.png using Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '7c61ab38c9cfe15e1827a64013695c56') in 0.0246709 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Import Request.
  Time since last request: 11.457311 seconds.
  path: Assets/Texture/grass.png
  artifactKey: Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b)
Start importing Assets/Texture/grass.png using Guid(fa11edb50364b88459c752062a333cf4) Importer(815301076,1909f56bfc062723c751e8b465ee728b) (PreviewImporter) -> (artifact id: '1cc8a183d8136bc23227b0261e1dec12') in 0.0201425 seconds
Number of updated asset objects reloaded before import = 0Number of asset objects unloaded after import = 2

========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.952 seconds
Refreshing native plugins compatible for Editor in 1.37 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.154 seconds
Domain Reload Profiling: 2110ms
	BeginReloadAssembly (278ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (59ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (588ms)
		LoadAssemblies (491ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (260ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (227ms)
			ResolveRequiredComponents (20ms)
	FinalizeReload (1155ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (887ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (128ms)
			ProcessInitializeOnLoadAttributes (650ms)
			ProcessInitializeOnLoadMethodAttributes (98ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 2.13 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (9.1 MB). Loaded Objects now: 6939.
Memory consumption went from 164.6 MB to 155.5 MB.
Total: 23.492600 ms (FindLiveObjects: 1.629900 ms CreateObjectMapping: 2.684400 ms MarkObjects: 9.260300 ms  DeleteObjects: 9.916400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.990 seconds
Refreshing native plugins compatible for Editor in 1.92 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.975 seconds
Domain Reload Profiling: 1970ms
	BeginReloadAssembly (304ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (67ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (566ms)
		LoadAssemblies (493ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (265ms)
			TypeCache.Refresh (17ms)
				TypeCache.ScanAssembly (1ms)
			BuildScriptInfoCaches (231ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (976ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (708ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (477ms)
			ProcessInitializeOnLoadMethodAttributes (64ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (17ms)
Refreshing native plugins compatible for Editor in 2.29 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.4 MB). Loaded Objects now: 6941.
Memory consumption went from 164.6 MB to 156.2 MB.
Total: 20.374300 ms (FindLiveObjects: 1.105200 ms CreateObjectMapping: 1.712300 ms MarkObjects: 10.316300 ms  DeleteObjects: 7.238300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.858 seconds
Refreshing native plugins compatible for Editor in 1.32 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.008 seconds
Domain Reload Profiling: 1870ms
	BeginReloadAssembly (245ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (514ms)
		LoadAssemblies (409ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (247ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (220ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1009ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (759ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (130ms)
			ProcessInitializeOnLoadAttributes (504ms)
			ProcessInitializeOnLoadMethodAttributes (114ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (22ms)
Refreshing native plugins compatible for Editor in 2.42 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.4 MB). Loaded Objects now: 6943.
Memory consumption went from 164.6 MB to 156.2 MB.
Total: 21.177000 ms (FindLiveObjects: 1.547700 ms CreateObjectMapping: 2.108800 ms MarkObjects: 8.861500 ms  DeleteObjects: 8.657700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.973 seconds
Refreshing native plugins compatible for Editor in 2.04 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.273 seconds
Domain Reload Profiling: 2249ms
	BeginReloadAssembly (282ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (56ms)
	RebuildCommonClasses (53ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (586ms)
		LoadAssemblies (494ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (265ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (237ms)
			ResolveRequiredComponents (15ms)
	FinalizeReload (1274ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (977ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (139ms)
			ProcessInitializeOnLoadAttributes (716ms)
			ProcessInitializeOnLoadMethodAttributes (110ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (24ms)
Refreshing native plugins compatible for Editor in 2.56 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.2 MB). Loaded Objects now: 6945.
Memory consumption went from 164.6 MB to 156.3 MB.
Total: 21.258500 ms (FindLiveObjects: 1.117100 ms CreateObjectMapping: 3.140300 ms MarkObjects: 8.796200 ms  DeleteObjects: 8.203100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.993 seconds
Refreshing native plugins compatible for Editor in 1.42 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.216 seconds
Domain Reload Profiling: 2212ms
	BeginReloadAssembly (282ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (64ms)
	RebuildCommonClasses (65ms)
	RebuildNativeTypeToScriptingClass (22ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (594ms)
		LoadAssemblies (475ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (282ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (251ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1216ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (902ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (144ms)
			ProcessInitializeOnLoadAttributes (618ms)
			ProcessInitializeOnLoadMethodAttributes (128ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (32ms)
Refreshing native plugins compatible for Editor in 2.49 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (9.4 MB). Loaded Objects now: 6947.
Memory consumption went from 164.6 MB to 155.2 MB.
Total: 25.119100 ms (FindLiveObjects: 1.762700 ms CreateObjectMapping: 2.511500 ms MarkObjects: 9.908200 ms  DeleteObjects: 10.934900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.000 seconds
Refreshing native plugins compatible for Editor in 1.74 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.211 seconds
Domain Reload Profiling: 2213ms
	BeginReloadAssembly (271ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (54ms)
	RebuildCommonClasses (48ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (626ms)
		LoadAssemblies (505ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (281ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (252ms)
			ResolveRequiredComponents (16ms)
	FinalizeReload (1211ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (914ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (6ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (183ms)
			ProcessInitializeOnLoadAttributes (597ms)
			ProcessInitializeOnLoadMethodAttributes (120ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 3.82 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.8 MB). Loaded Objects now: 6949.
Memory consumption went from 164.6 MB to 155.8 MB.
Total: 26.624900 ms (FindLiveObjects: 1.641500 ms CreateObjectMapping: 3.012300 ms MarkObjects: 11.479300 ms  DeleteObjects: 10.490100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.006 seconds
Refreshing native plugins compatible for Editor in 1.47 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.195 seconds
Domain Reload Profiling: 2205ms
	BeginReloadAssembly (280ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (62ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (39ms)
	LoadAllAssembliesAndSetupDomain (625ms)
		LoadAssemblies (492ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (295ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (262ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1196ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (886ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (159ms)
			ProcessInitializeOnLoadAttributes (600ms)
			ProcessInitializeOnLoadMethodAttributes (116ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (24ms)
Refreshing native plugins compatible for Editor in 14.62 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (9.0 MB). Loaded Objects now: 6951.
Memory consumption went from 164.6 MB to 155.6 MB.
Total: 28.167000 ms (FindLiveObjects: 4.108600 ms CreateObjectMapping: 3.174300 ms MarkObjects: 10.131500 ms  DeleteObjects: 10.750800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.018 seconds
Refreshing native plugins compatible for Editor in 1.31 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.321 seconds
Domain Reload Profiling: 2344ms
	BeginReloadAssembly (295ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (61ms)
	RebuildCommonClasses (54ms)
	RebuildNativeTypeToScriptingClass (24ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (615ms)
		LoadAssemblies (492ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (291ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (262ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1323ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (1041ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (145ms)
			ProcessInitializeOnLoadAttributes (618ms)
			ProcessInitializeOnLoadMethodAttributes (265ms)
			AfterProcessingInitializeOnLoad (5ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 5.79 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (11.0 MB). Loaded Objects now: 6953.
Memory consumption went from 164.6 MB to 153.7 MB.
Total: 35.337100 ms (FindLiveObjects: 1.545000 ms CreateObjectMapping: 4.027700 ms MarkObjects: 13.383700 ms  DeleteObjects: 16.378200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.972 seconds
Refreshing native plugins compatible for Editor in 1.40 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.267 seconds
Domain Reload Profiling: 2242ms
	BeginReloadAssembly (257ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (64ms)
	initialDomainReloadingComplete (33ms)
	LoadAllAssembliesAndSetupDomain (574ms)
		LoadAssemblies (460ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (266ms)
			TypeCache.Refresh (15ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (235ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1268ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (878ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (201ms)
			ProcessInitializeOnLoadAttributes (584ms)
			ProcessInitializeOnLoadMethodAttributes (80ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (14ms)
Refreshing native plugins compatible for Editor in 2.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.6 MB). Loaded Objects now: 6955.
Memory consumption went from 164.6 MB to 156.0 MB.
Total: 20.399100 ms (FindLiveObjects: 1.662000 ms CreateObjectMapping: 2.021200 ms MarkObjects: 8.448600 ms  DeleteObjects: 8.265600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.911 seconds
Refreshing native plugins compatible for Editor in 1.26 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.010 seconds
Domain Reload Profiling: 1924ms
	BeginReloadAssembly (260ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (59ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (37ms)
	LoadAllAssembliesAndSetupDomain (537ms)
		LoadAssemblies (426ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (262ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (237ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1011ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (753ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (3ms)
			BeforeProcessingInitializeOnLoad (135ms)
			ProcessInitializeOnLoadAttributes (497ms)
			ProcessInitializeOnLoadMethodAttributes (111ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 2.72 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (9.0 MB). Loaded Objects now: 6957.
Memory consumption went from 164.6 MB to 155.5 MB.
Total: 23.499500 ms (FindLiveObjects: 1.332700 ms CreateObjectMapping: 2.124100 ms MarkObjects: 9.678000 ms  DeleteObjects: 10.362800 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.910 seconds
Refreshing native plugins compatible for Editor in 1.36 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.989 seconds
Domain Reload Profiling: 1901ms
	BeginReloadAssembly (270ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (65ms)
	RebuildCommonClasses (50ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (34ms)
	LoadAllAssembliesAndSetupDomain (538ms)
		LoadAssemblies (417ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (273ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (239ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (989ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (747ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (123ms)
			ProcessInitializeOnLoadAttributes (544ms)
			ProcessInitializeOnLoadMethodAttributes (69ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 1.97 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.2 MB). Loaded Objects now: 6959.
Memory consumption went from 164.6 MB to 156.5 MB.
Total: 17.989700 ms (FindLiveObjects: 1.026700 ms CreateObjectMapping: 1.489800 ms MarkObjects: 8.952500 ms  DeleteObjects: 6.519500 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.899 seconds
Refreshing native plugins compatible for Editor in 1.27 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.031 seconds
Domain Reload Profiling: 1933ms
	BeginReloadAssembly (257ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (59ms)
	RebuildCommonClasses (50ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (538ms)
		LoadAssemblies (419ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (263ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (238ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1032ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (781ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (135ms)
			ProcessInitializeOnLoadAttributes (511ms)
			ProcessInitializeOnLoadMethodAttributes (123ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (1ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (24ms)
Refreshing native plugins compatible for Editor in 2.72 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (9.0 MB). Loaded Objects now: 6961.
Memory consumption went from 164.6 MB to 155.6 MB.
Total: 27.706100 ms (FindLiveObjects: 1.805500 ms CreateObjectMapping: 2.286300 ms MarkObjects: 9.432500 ms  DeleteObjects: 14.180200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 3.57 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6180 unused Assets / (8.9 MB). Loaded Objects now: 6961.
Memory consumption went from 164.8 MB to 155.9 MB.
Total: 23.971100 ms (FindLiveObjects: 1.267400 ms CreateObjectMapping: 2.818700 ms MarkObjects: 10.579300 ms  DeleteObjects: 9.304300 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.849 seconds
Refreshing native plugins compatible for Editor in 1.72 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.086 seconds
Domain Reload Profiling: 1938ms
	BeginReloadAssembly (256ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (26ms)
	LoadAllAssembliesAndSetupDomain (503ms)
		LoadAssemblies (409ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (239ms)
			TypeCache.Refresh (11ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (211ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1087ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (839ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (127ms)
			ProcessInitializeOnLoadAttributes (601ms)
			ProcessInitializeOnLoadMethodAttributes (100ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 2.42 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.8 MB). Loaded Objects now: 6963.
Memory consumption went from 164.6 MB to 155.8 MB.
Total: 23.108400 ms (FindLiveObjects: 1.346400 ms CreateObjectMapping: 2.303500 ms MarkObjects: 9.945600 ms  DeleteObjects: 9.511200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.868 seconds
Refreshing native plugins compatible for Editor in 1.39 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.036 seconds
Domain Reload Profiling: 1906ms
	BeginReloadAssembly (246ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (528ms)
		LoadAssemblies (407ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (267ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (243ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (1037ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (775ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (147ms)
			ProcessInitializeOnLoadAttributes (507ms)
			ProcessInitializeOnLoadMethodAttributes (109ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (20ms)
Refreshing native plugins compatible for Editor in 4.22 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.6 MB). Loaded Objects now: 6965.
Memory consumption went from 164.6 MB to 156.1 MB.
Total: 22.955300 ms (FindLiveObjects: 1.614400 ms CreateObjectMapping: 3.149200 ms MarkObjects: 9.759400 ms  DeleteObjects: 8.430600 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.839 seconds
Refreshing native plugins compatible for Editor in 1.23 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.925 seconds
Domain Reload Profiling: 1767ms
	BeginReloadAssembly (244ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (50ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (27ms)
	LoadAllAssembliesAndSetupDomain (512ms)
		LoadAssemblies (406ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (250ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (226ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (926ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (679ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (120ms)
			ProcessInitializeOnLoadAttributes (472ms)
			ProcessInitializeOnLoadMethodAttributes (79ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (18ms)
Refreshing native plugins compatible for Editor in 3.25 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.6 MB). Loaded Objects now: 6967.
Memory consumption went from 164.6 MB to 156.0 MB.
Total: 21.915600 ms (FindLiveObjects: 1.378800 ms CreateObjectMapping: 2.205700 ms MarkObjects: 9.748200 ms  DeleteObjects: 8.580900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.985 seconds
Refreshing native plugins compatible for Editor in 1.30 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.975 seconds
Domain Reload Profiling: 1962ms
	BeginReloadAssembly (270ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (10ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (52ms)
	RebuildCommonClasses (49ms)
	RebuildNativeTypeToScriptingClass (25ms)
	initialDomainReloadingComplete (44ms)
	LoadAllAssembliesAndSetupDomain (599ms)
		LoadAssemblies (503ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (254ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (229ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (976ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (737ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (130ms)
			ProcessInitializeOnLoadAttributes (512ms)
			ProcessInitializeOnLoadMethodAttributes (83ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 2.05 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.2 MB). Loaded Objects now: 6969.
Memory consumption went from 164.6 MB to 156.4 MB.
Total: 18.424700 ms (FindLiveObjects: 1.012500 ms CreateObjectMapping: 1.960600 ms MarkObjects: 8.520700 ms  DeleteObjects: 6.929700 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.962 seconds
Refreshing native plugins compatible for Editor in 1.61 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.097 seconds
Domain Reload Profiling: 2063ms
	BeginReloadAssembly (289ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (55ms)
	RebuildCommonClasses (46ms)
	RebuildNativeTypeToScriptingClass (18ms)
	initialDomainReloadingComplete (35ms)
	LoadAllAssembliesAndSetupDomain (577ms)
		LoadAssemblies (502ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (256ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (230ms)
			ResolveRequiredComponents (14ms)
	FinalizeReload (1098ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (813ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (140ms)
			ProcessInitializeOnLoadAttributes (564ms)
			ProcessInitializeOnLoadMethodAttributes (99ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (21ms)
Refreshing native plugins compatible for Editor in 2.92 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.3 MB). Loaded Objects now: 6971.
Memory consumption went from 164.6 MB to 156.4 MB.
Total: 23.564700 ms (FindLiveObjects: 1.576300 ms CreateObjectMapping: 2.194600 ms MarkObjects: 10.608400 ms  DeleteObjects: 9.183200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 2.02 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6180 unused Assets / (8.2 MB). Loaded Objects now: 6971.
Memory consumption went from 164.8 MB to 156.6 MB.
Total: 18.632900 ms (FindLiveObjects: 1.129400 ms CreateObjectMapping: 1.863500 ms MarkObjects: 8.843600 ms  DeleteObjects: 6.793900 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.947 seconds
Refreshing native plugins compatible for Editor in 1.34 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.138 seconds
Domain Reload Profiling: 2088ms
	BeginReloadAssembly (304ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (64ms)
	RebuildCommonClasses (47ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (30ms)
	LoadAllAssembliesAndSetupDomain (547ms)
		LoadAssemblies (458ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (271ms)
			TypeCache.Refresh (13ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (227ms)
			ResolveRequiredComponents (27ms)
	FinalizeReload (1139ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (805ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (5ms)
			BeforeProcessingInitializeOnLoad (155ms)
			ProcessInitializeOnLoadAttributes (542ms)
			ProcessInitializeOnLoadMethodAttributes (97ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (19ms)
Refreshing native plugins compatible for Editor in 2.24 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.4 MB). Loaded Objects now: 6973.
Memory consumption went from 164.6 MB to 156.3 MB.
Total: 19.355100 ms (FindLiveObjects: 1.137300 ms CreateObjectMapping: 1.962700 ms MarkObjects: 9.014000 ms  DeleteObjects: 7.239200 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.959 seconds
Refreshing native plugins compatible for Editor in 1.98 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.007 seconds
Domain Reload Profiling: 1968ms
	BeginReloadAssembly (243ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (48ms)
	RebuildCommonClasses (43ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (32ms)
	LoadAllAssembliesAndSetupDomain (627ms)
		LoadAssemblies (489ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (278ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (247ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (1007ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (741ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (125ms)
			ProcessInitializeOnLoadAttributes (513ms)
			ProcessInitializeOnLoadMethodAttributes (93ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 2.86 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (9.5 MB). Loaded Objects now: 6975.
Memory consumption went from 164.6 MB to 155.2 MB.
Total: 25.088300 ms (FindLiveObjects: 1.642000 ms CreateObjectMapping: 2.592100 ms MarkObjects: 9.940700 ms  DeleteObjects: 10.912100 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.102 seconds
Refreshing native plugins compatible for Editor in 2.55 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.194 seconds
Domain Reload Profiling: 2299ms
	BeginReloadAssembly (307ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (8ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (63ms)
	RebuildCommonClasses (57ms)
	RebuildNativeTypeToScriptingClass (17ms)
	initialDomainReloadingComplete (36ms)
	LoadAllAssembliesAndSetupDomain (687ms)
		LoadAssemblies (520ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (352ms)
			TypeCache.Refresh (9ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (319ms)
			ResolveRequiredComponents (18ms)
	FinalizeReload (1194ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (893ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (4ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (158ms)
			ProcessInitializeOnLoadAttributes (602ms)
			ProcessInitializeOnLoadMethodAttributes (121ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (25ms)
Refreshing native plugins compatible for Editor in 2.56 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (9.1 MB). Loaded Objects now: 6977.
Memory consumption went from 164.7 MB to 155.6 MB.
Total: 25.265600 ms (FindLiveObjects: 2.264900 ms CreateObjectMapping: 2.198800 ms MarkObjects: 10.610700 ms  DeleteObjects: 10.190000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Refreshing native plugins compatible for Editor in 4.52 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6180 unused Assets / (8.3 MB). Loaded Objects now: 6977.
Memory consumption went from 164.8 MB to 156.5 MB.
Total: 26.573100 ms (FindLiveObjects: 1.436800 ms CreateObjectMapping: 3.173700 ms MarkObjects: 14.122700 ms  DeleteObjects: 7.838400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  1.067 seconds
Refreshing native plugins compatible for Editor in 1.89 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.914 seconds
Domain Reload Profiling: 1985ms
	BeginReloadAssembly (437ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (11ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (206ms)
	RebuildCommonClasses (44ms)
	RebuildNativeTypeToScriptingClass (16ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (545ms)
		LoadAssemblies (436ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (270ms)
			TypeCache.Refresh (10ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (240ms)
			ResolveRequiredComponents (17ms)
	FinalizeReload (915ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (685ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (131ms)
			ProcessInitializeOnLoadAttributes (476ms)
			ProcessInitializeOnLoadMethodAttributes (69ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.11 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (7.3 MB). Loaded Objects now: 6979.
Memory consumption went from 164.7 MB to 157.3 MB.
Total: 16.617400 ms (FindLiveObjects: 1.060300 ms CreateObjectMapping: 1.871700 ms MarkObjects: 7.896400 ms  DeleteObjects: 5.787200 ms)

Prepare: number of updated asset objects reloaded= 1
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.877 seconds
Refreshing native plugins compatible for Editor in 1.52 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  1.023 seconds
Domain Reload Profiling: 1904ms
	BeginReloadAssembly (253ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (53ms)
	RebuildCommonClasses (42ms)
	RebuildNativeTypeToScriptingClass (19ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (537ms)
		LoadAssemblies (414ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (273ms)
			TypeCache.Refresh (8ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (249ms)
			ResolveRequiredComponents (12ms)
	FinalizeReload (1023ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (773ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (147ms)
			ProcessInitializeOnLoadAttributes (502ms)
			ProcessInitializeOnLoadMethodAttributes (114ms)
			AfterProcessingInitializeOnLoad (3ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (23ms)
Refreshing native plugins compatible for Editor in 4.16 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6188 unused Assets / (8.5 MB). Loaded Objects now: 6980.
Memory consumption went from 164.8 MB to 156.3 MB.
Total: 22.484400 ms (FindLiveObjects: 1.152600 ms CreateObjectMapping: 2.546100 ms MarkObjects: 10.000300 ms  DeleteObjects: 8.784000 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.967 seconds
Refreshing native plugins compatible for Editor in 1.49 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.966 seconds
Domain Reload Profiling: 1937ms
	BeginReloadAssembly (270ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (9ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (21ms)
	initialDomainReloadingComplete (29ms)
	LoadAllAssembliesAndSetupDomain (605ms)
		LoadAssemblies (525ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (243ms)
			TypeCache.Refresh (14ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (212ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (967ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (701ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (5ms)
			SetLoadedEditorAssemblies (6ms)
			BeforeProcessingInitializeOnLoad (151ms)
			ProcessInitializeOnLoadAttributes (466ms)
			ProcessInitializeOnLoadMethodAttributes (70ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (13ms)
Refreshing native plugins compatible for Editor in 2.21 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (8.4 MB). Loaded Objects now: 6982.
Memory consumption went from 164.9 MB to 156.5 MB.
Total: 17.977700 ms (FindLiveObjects: 1.209500 ms CreateObjectMapping: 1.851300 ms MarkObjects: 7.957700 ms  DeleteObjects: 6.957400 ms)

Prepare: number of updated asset objects reloaded= 0
========================================================================
Received Prepare
Begin MonoManager ReloadAssembly
- Loaded All Assemblies, in  0.937 seconds
Refreshing native plugins compatible for Editor in 1.26 ms, found 3 plugins.
Native extension for WindowsStandalone target not found
Native extension for WebGL target not found
Mono: successfully reloaded assembly
- Finished resetting the current domain, in  0.993 seconds
Domain Reload Profiling: 1935ms
	BeginReloadAssembly (259ms)
		ExecutionOrderSort (0ms)
		DisableScriptedObjects (7ms)
		BackupInstance (0ms)
		ReleaseScriptingObjects (0ms)
		CreateAndSetChildDomain (51ms)
	RebuildCommonClasses (45ms)
	RebuildNativeTypeToScriptingClass (20ms)
	initialDomainReloadingComplete (28ms)
	LoadAllAssembliesAndSetupDomain (587ms)
		LoadAssemblies (475ms)
		RebuildTransferFunctionScriptingTraits (0ms)
		AnalyzeDomain (271ms)
			TypeCache.Refresh (7ms)
				TypeCache.ScanAssembly (0ms)
			BuildScriptInfoCaches (247ms)
			ResolveRequiredComponents (13ms)
	FinalizeReload (995ms)
		ReleaseScriptCaches (0ms)
		RebuildScriptCaches (0ms)
		SetupLoadedEditorAssemblies (755ms)
			LogAssemblyErrors (0ms)
			InitializePlatformSupportModulesInManaged (3ms)
			SetLoadedEditorAssemblies (4ms)
			BeforeProcessingInitializeOnLoad (139ms)
			ProcessInitializeOnLoadAttributes (523ms)
			ProcessInitializeOnLoadMethodAttributes (84ms)
			AfterProcessingInitializeOnLoad (2ms)
			EditorAssembliesLoaded (0ms)
		ExecutionOrderSort2 (0ms)
		AwakeInstancesAfterBackupRestoration (16ms)
Refreshing native plugins compatible for Editor in 2.16 ms, found 3 plugins.
Preloading 0 native plugins for Editor in 0.00 ms.
Unloading 47 Unused Serialized files (Serialized files now loaded: 0)
Unloading 6187 unused Assets / (9.2 MB). Loaded Objects now: 6984.
Memory consumption went from 164.8 MB to 155.7 MB.
Total: 27.382900 ms (FindLiveObjects: 1.976700 ms CreateObjectMapping: 3.045500 ms MarkObjects: 11.915000 ms  DeleteObjects: 10.444400 ms)

Prepare: number of updated asset objects reloaded= 0
