{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 6348, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 6348, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 6348, "tid": 658, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 6348, "tid": 658, "ts": 1751408074102413, "dur": 745, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 6348, "tid": 658, "ts": 1751408074109022, "dur": 974, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 6348, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 6348, "tid": 1, "ts": 1751408062417475, "dur": 7166, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 6348, "tid": 1, "ts": 1751408062424646, "dur": 76020, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 6348, "tid": 1, "ts": 1751408062500677, "dur": 58185, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 6348, "tid": 658, "ts": 1751408074110000, "dur": 13, "ph": "X", "name": "", "args": {}}, {"pid": 6348, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062415287, "dur": 5636, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062420927, "dur": 11670753, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062422358, "dur": 3862, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062426229, "dur": 2498, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062428731, "dur": 357, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429093, "dur": 16, "ph": "X", "name": "ProcessMessages 20519", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429111, "dur": 120, "ph": "X", "name": "ReadAsync 20519", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429234, "dur": 1, "ph": "X", "name": "ProcessMessages 887", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429237, "dur": 51, "ph": "X", "name": "ReadAsync 887", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429291, "dur": 1, "ph": "X", "name": "ProcessMessages 810", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429295, "dur": 86, "ph": "X", "name": "ReadAsync 810", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429386, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429426, "dur": 1, "ph": "X", "name": "ProcessMessages 75", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429429, "dur": 40, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429471, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429473, "dur": 39, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429515, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429517, "dur": 33, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429552, "dur": 1, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429553, "dur": 40, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429596, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429598, "dur": 35, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429636, "dur": 1, "ph": "X", "name": "ProcessMessages 278", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429638, "dur": 35, "ph": "X", "name": "ReadAsync 278", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429675, "dur": 1, "ph": "X", "name": "ProcessMessages 262", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429677, "dur": 40, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429719, "dur": 1, "ph": "X", "name": "ProcessMessages 387", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429721, "dur": 37, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429761, "dur": 1, "ph": "X", "name": "ProcessMessages 397", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429762, "dur": 40, "ph": "X", "name": "ReadAsync 397", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429805, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429808, "dur": 39, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429850, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429851, "dur": 39, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429893, "dur": 1, "ph": "X", "name": "ProcessMessages 307", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429895, "dur": 32, "ph": "X", "name": "ReadAsync 307", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429931, "dur": 47, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429981, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062429984, "dur": 50, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430036, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430039, "dur": 45, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430088, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430090, "dur": 45, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430139, "dur": 97, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430239, "dur": 1, "ph": "X", "name": "ProcessMessages 473", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430240, "dur": 75, "ph": "X", "name": "ReadAsync 473", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430319, "dur": 1, "ph": "X", "name": "ProcessMessages 213", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430321, "dur": 51, "ph": "X", "name": "ReadAsync 213", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430375, "dur": 1, "ph": "X", "name": "ProcessMessages 1341", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430379, "dur": 79, "ph": "X", "name": "ReadAsync 1341", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430463, "dur": 44, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430509, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430512, "dur": 46, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430559, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430561, "dur": 39, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430602, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430604, "dur": 46, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430652, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430654, "dur": 42, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430699, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430701, "dur": 42, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430745, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430747, "dur": 42, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430792, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430794, "dur": 44, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430840, "dur": 1, "ph": "X", "name": "ProcessMessages 536", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430842, "dur": 42, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430886, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430888, "dur": 43, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430933, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430935, "dur": 47, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430984, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062430987, "dur": 48, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431039, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431041, "dur": 43, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431087, "dur": 1, "ph": "X", "name": "ProcessMessages 168", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431090, "dur": 44, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431136, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431138, "dur": 43, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431184, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431186, "dur": 47, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431237, "dur": 47, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431287, "dur": 1, "ph": "X", "name": "ProcessMessages 380", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431289, "dur": 41, "ph": "X", "name": "ReadAsync 380", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431332, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431334, "dur": 36, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431373, "dur": 1, "ph": "X", "name": "ProcessMessages 280", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431375, "dur": 40, "ph": "X", "name": "ReadAsync 280", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431417, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431419, "dur": 46, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431469, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431473, "dur": 43, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431519, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431521, "dur": 38, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431562, "dur": 36, "ph": "X", "name": "ReadAsync 232", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431601, "dur": 1, "ph": "X", "name": "ProcessMessages 352", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431602, "dur": 34, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431638, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431640, "dur": 35, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431681, "dur": 35, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431718, "dur": 1, "ph": "X", "name": "ProcessMessages 329", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431720, "dur": 30, "ph": "X", "name": "ReadAsync 329", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431755, "dur": 32, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431790, "dur": 39, "ph": "X", "name": "ReadAsync 314", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431831, "dur": 1, "ph": "X", "name": "ProcessMessages 705", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431833, "dur": 34, "ph": "X", "name": "ReadAsync 705", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431870, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431872, "dur": 36, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431909, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431911, "dur": 31, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431944, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431946, "dur": 33, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431982, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062431984, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432039, "dur": 2, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432042, "dur": 47, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432092, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432094, "dur": 42, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432139, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432141, "dur": 32, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432177, "dur": 34, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432214, "dur": 35, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432252, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432253, "dur": 44, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432299, "dur": 1, "ph": "X", "name": "ProcessMessages 369", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432301, "dur": 37, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432340, "dur": 1, "ph": "X", "name": "ProcessMessages 555", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432342, "dur": 31, "ph": "X", "name": "ReadAsync 555", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432375, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432376, "dur": 26, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432406, "dur": 37, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432445, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432447, "dur": 34, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432482, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432484, "dur": 36, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432524, "dur": 34, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432560, "dur": 1, "ph": "X", "name": "ProcessMessages 336", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432562, "dur": 32, "ph": "X", "name": "ReadAsync 336", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432596, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432598, "dur": 33, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432634, "dur": 35, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432670, "dur": 1, "ph": "X", "name": "ProcessMessages 327", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432672, "dur": 35, "ph": "X", "name": "ReadAsync 327", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432710, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432711, "dur": 33, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432746, "dur": 1, "ph": "X", "name": "ProcessMessages 308", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432748, "dur": 37, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432788, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432789, "dur": 60, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432853, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432890, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432892, "dur": 35, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432929, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432931, "dur": 38, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432972, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062432974, "dur": 44, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433021, "dur": 2, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433024, "dur": 44, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433072, "dur": 1, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433075, "dur": 49, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433127, "dur": 1, "ph": "X", "name": "ProcessMessages 745", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433129, "dur": 45, "ph": "X", "name": "ReadAsync 745", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433176, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433178, "dur": 42, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433222, "dur": 1, "ph": "X", "name": "ProcessMessages 516", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433224, "dur": 31, "ph": "X", "name": "ReadAsync 516", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433258, "dur": 29, "ph": "X", "name": "ReadAsync 283", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433290, "dur": 37, "ph": "X", "name": "ReadAsync 130", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433329, "dur": 1, "ph": "X", "name": "ProcessMessages 495", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433330, "dur": 36, "ph": "X", "name": "ReadAsync 495", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433368, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433370, "dur": 34, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433406, "dur": 1, "ph": "X", "name": "ProcessMessages 426", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433408, "dur": 36, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433445, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433447, "dur": 31, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433480, "dur": 1, "ph": "X", "name": "ProcessMessages 298", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433482, "dur": 159, "ph": "X", "name": "ReadAsync 298", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433644, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433683, "dur": 1, "ph": "X", "name": "ProcessMessages 524", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433686, "dur": 37, "ph": "X", "name": "ReadAsync 524", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433724, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433727, "dur": 35, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433765, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433766, "dur": 33, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433801, "dur": 1, "ph": "X", "name": "ProcessMessages 376", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433803, "dur": 31, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433835, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433837, "dur": 28, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433868, "dur": 36, "ph": "X", "name": "ReadAsync 131", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433907, "dur": 1, "ph": "X", "name": "ProcessMessages 461", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433909, "dur": 36, "ph": "X", "name": "ReadAsync 461", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433946, "dur": 1, "ph": "X", "name": "ProcessMessages 482", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433948, "dur": 40, "ph": "X", "name": "ReadAsync 482", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433991, "dur": 2, "ph": "X", "name": "ProcessMessages 304", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062433994, "dur": 45, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434043, "dur": 1, "ph": "X", "name": "ProcessMessages 535", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434046, "dur": 45, "ph": "X", "name": "ReadAsync 535", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434093, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434095, "dur": 39, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434136, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434137, "dur": 35, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434176, "dur": 33, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434211, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434213, "dur": 32, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434248, "dur": 29, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434281, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434318, "dur": 1, "ph": "X", "name": "ProcessMessages 498", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434319, "dur": 33, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434354, "dur": 1, "ph": "X", "name": "ProcessMessages 444", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434356, "dur": 35, "ph": "X", "name": "ReadAsync 444", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434392, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434394, "dur": 34, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434432, "dur": 31, "ph": "X", "name": "ReadAsync 390", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434465, "dur": 1, "ph": "X", "name": "ProcessMessages 351", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434469, "dur": 35, "ph": "X", "name": "ReadAsync 351", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434508, "dur": 34, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434545, "dur": 1, "ph": "X", "name": "ProcessMessages 427", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434546, "dur": 35, "ph": "X", "name": "ReadAsync 427", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434585, "dur": 34, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434622, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434623, "dur": 30, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434656, "dur": 31, "ph": "X", "name": "ReadAsync 308", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434689, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434690, "dur": 34, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434726, "dur": 1, "ph": "X", "name": "ProcessMessages 450", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434728, "dur": 34, "ph": "X", "name": "ReadAsync 450", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434764, "dur": 1, "ph": "X", "name": "ProcessMessages 531", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434765, "dur": 36, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434803, "dur": 1, "ph": "X", "name": "ProcessMessages 405", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434805, "dur": 35, "ph": "X", "name": "ReadAsync 405", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434842, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434844, "dur": 33, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434879, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434881, "dur": 32, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434915, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434917, "dur": 37, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434956, "dur": 1, "ph": "X", "name": "ProcessMessages 699", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062434958, "dur": 49, "ph": "X", "name": "ReadAsync 699", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435010, "dur": 2, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435013, "dur": 50, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435066, "dur": 1, "ph": "X", "name": "ProcessMessages 584", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435068, "dur": 37, "ph": "X", "name": "ReadAsync 584", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435107, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435109, "dur": 37, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435149, "dur": 1, "ph": "X", "name": "ProcessMessages 378", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435151, "dur": 36, "ph": "X", "name": "ReadAsync 378", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435188, "dur": 1, "ph": "X", "name": "ProcessMessages 628", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435190, "dur": 35, "ph": "X", "name": "ReadAsync 628", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435227, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435228, "dur": 35, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435267, "dur": 35, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435303, "dur": 1, "ph": "X", "name": "ProcessMessages 494", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435305, "dur": 28, "ph": "X", "name": "ReadAsync 494", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435335, "dur": 2, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435338, "dur": 38, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435378, "dur": 1, "ph": "X", "name": "ProcessMessages 684", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435380, "dur": 32, "ph": "X", "name": "ReadAsync 684", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435414, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435415, "dur": 35, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435454, "dur": 36, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435492, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435493, "dur": 35, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435531, "dur": 1, "ph": "X", "name": "ProcessMessages 416", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435533, "dur": 33, "ph": "X", "name": "ReadAsync 416", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435568, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435569, "dur": 35, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435606, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435608, "dur": 34, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435646, "dur": 34, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435682, "dur": 1, "ph": "X", "name": "ProcessMessages 364", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435684, "dur": 39, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435726, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435729, "dur": 39, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435771, "dur": 1, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435773, "dur": 44, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435820, "dur": 1, "ph": "X", "name": "ProcessMessages 585", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435822, "dur": 38, "ph": "X", "name": "ReadAsync 585", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435862, "dur": 1, "ph": "X", "name": "ProcessMessages 398", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435864, "dur": 41, "ph": "X", "name": "ReadAsync 398", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435908, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435909, "dur": 39, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435951, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435953, "dur": 26, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435982, "dur": 1, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062435985, "dur": 51, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436039, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436042, "dur": 44, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436089, "dur": 1, "ph": "X", "name": "ProcessMessages 621", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436092, "dur": 39, "ph": "X", "name": "ReadAsync 621", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436134, "dur": 1, "ph": "X", "name": "ProcessMessages 459", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436136, "dur": 39, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436178, "dur": 28, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436210, "dur": 34, "ph": "X", "name": "ReadAsync 85", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436246, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436248, "dur": 32, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436283, "dur": 37, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436324, "dur": 33, "ph": "X", "name": "ReadAsync 653", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436359, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436360, "dur": 37, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436400, "dur": 27, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436430, "dur": 35, "ph": "X", "name": "ReadAsync 87", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436467, "dur": 1, "ph": "X", "name": "ProcessMessages 399", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436469, "dur": 39, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436510, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436512, "dur": 57, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436572, "dur": 1, "ph": "X", "name": "ProcessMessages 432", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436574, "dur": 42, "ph": "X", "name": "ReadAsync 432", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436619, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436621, "dur": 30, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436654, "dur": 34, "ph": "X", "name": "ReadAsync 168", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436692, "dur": 29, "ph": "X", "name": "ReadAsync 426", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436724, "dur": 37, "ph": "X", "name": "ReadAsync 226", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436766, "dur": 35, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436803, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436805, "dur": 35, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436842, "dur": 1, "ph": "X", "name": "ProcessMessages 389", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436843, "dur": 29, "ph": "X", "name": "ReadAsync 389", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436875, "dur": 35, "ph": "X", "name": "ReadAsync 79", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436913, "dur": 35, "ph": "X", "name": "ReadAsync 536", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436951, "dur": 43, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436997, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062436999, "dur": 49, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437052, "dur": 2, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437055, "dur": 40, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437098, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437100, "dur": 40, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437142, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437144, "dur": 38, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437183, "dur": 1, "ph": "X", "name": "ProcessMessages 430", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437185, "dur": 37, "ph": "X", "name": "ReadAsync 430", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437224, "dur": 1, "ph": "X", "name": "ProcessMessages 425", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437226, "dur": 35, "ph": "X", "name": "ReadAsync 425", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437264, "dur": 1, "ph": "X", "name": "ProcessMessages 332", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437266, "dur": 31, "ph": "X", "name": "ReadAsync 332", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437300, "dur": 34, "ph": "X", "name": "ReadAsync 274", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437336, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437337, "dur": 36, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437376, "dur": 1, "ph": "X", "name": "ProcessMessages 521", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437377, "dur": 35, "ph": "X", "name": "ReadAsync 521", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437414, "dur": 1, "ph": "X", "name": "ProcessMessages 345", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437416, "dur": 34, "ph": "X", "name": "ReadAsync 345", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437452, "dur": 1, "ph": "X", "name": "ProcessMessages 317", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437453, "dur": 35, "ph": "X", "name": "ReadAsync 317", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437490, "dur": 1, "ph": "X", "name": "ProcessMessages 449", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437491, "dur": 28, "ph": "X", "name": "ReadAsync 449", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437523, "dur": 36, "ph": "X", "name": "ReadAsync 77", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437561, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437563, "dur": 40, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437605, "dur": 1, "ph": "X", "name": "ProcessMessages 677", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437608, "dur": 36, "ph": "X", "name": "ReadAsync 677", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437645, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437647, "dur": 37, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437686, "dur": 1, "ph": "X", "name": "ProcessMessages 359", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437688, "dur": 32, "ph": "X", "name": "ReadAsync 359", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437722, "dur": 2, "ph": "X", "name": "ProcessMessages 301", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437725, "dur": 36, "ph": "X", "name": "ReadAsync 301", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437763, "dur": 1, "ph": "X", "name": "ProcessMessages 465", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437765, "dur": 36, "ph": "X", "name": "ReadAsync 465", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437804, "dur": 36, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437843, "dur": 1, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437844, "dur": 35, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437883, "dur": 34, "ph": "X", "name": "ReadAsync 529", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437919, "dur": 1, "ph": "X", "name": "ProcessMessages 394", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437920, "dur": 34, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437957, "dur": 39, "ph": "X", "name": "ReadAsync 328", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062437999, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438002, "dur": 47, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438052, "dur": 1, "ph": "X", "name": "ProcessMessages 840", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438055, "dur": 43, "ph": "X", "name": "ReadAsync 840", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438101, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438103, "dur": 38, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438143, "dur": 1, "ph": "X", "name": "ProcessMessages 404", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438145, "dur": 35, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438184, "dur": 38, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438225, "dur": 36, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438262, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438264, "dur": 34, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438302, "dur": 35, "ph": "X", "name": "ReadAsync 445", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438339, "dur": 1, "ph": "X", "name": "ProcessMessages 342", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438341, "dur": 26, "ph": "X", "name": "ReadAsync 342", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438371, "dur": 28, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438403, "dur": 36, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438441, "dur": 1, "ph": "X", "name": "ProcessMessages 542", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438443, "dur": 35, "ph": "X", "name": "ReadAsync 542", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438480, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438482, "dur": 36, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438520, "dur": 1, "ph": "X", "name": "ProcessMessages 350", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438521, "dur": 33, "ph": "X", "name": "ReadAsync 350", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438556, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438558, "dur": 31, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438592, "dur": 32, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438627, "dur": 39, "ph": "X", "name": "ReadAsync 284", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438669, "dur": 1, "ph": "X", "name": "ProcessMessages 434", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438670, "dur": 37, "ph": "X", "name": "ReadAsync 434", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438710, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438711, "dur": 35, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438748, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438750, "dur": 28, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438798, "dur": 35, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062438835, "dur": 163, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439000, "dur": 84, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439087, "dur": 4, "ph": "X", "name": "ProcessMessages 3008", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439093, "dur": 51, "ph": "X", "name": "ReadAsync 3008", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439147, "dur": 1, "ph": "X", "name": "ProcessMessages 548", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439149, "dur": 54, "ph": "X", "name": "ReadAsync 548", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439207, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439210, "dur": 46, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439259, "dur": 1, "ph": "X", "name": "ProcessMessages 82", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439261, "dur": 41, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439304, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439305, "dur": 36, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439344, "dur": 1, "ph": "X", "name": "ProcessMessages 470", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439345, "dur": 36, "ph": "X", "name": "ReadAsync 470", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439385, "dur": 35, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439422, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439424, "dur": 32, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439458, "dur": 1, "ph": "X", "name": "ProcessMessages 310", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439460, "dur": 36, "ph": "X", "name": "ReadAsync 310", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439499, "dur": 35, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439536, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439537, "dur": 34, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439574, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439576, "dur": 45, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439623, "dur": 1, "ph": "X", "name": "ProcessMessages 379", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439625, "dur": 39, "ph": "X", "name": "ReadAsync 379", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439667, "dur": 1, "ph": "X", "name": "ProcessMessages 159", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439669, "dur": 38, "ph": "X", "name": "ReadAsync 159", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439712, "dur": 29, "ph": "X", "name": "ReadAsync 218", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439744, "dur": 1, "ph": "X", "name": "ProcessMessages 71", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439745, "dur": 36, "ph": "X", "name": "ReadAsync 71", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439785, "dur": 32, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439819, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439821, "dur": 36, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439859, "dur": 1, "ph": "X", "name": "ProcessMessages 403", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439861, "dur": 49, "ph": "X", "name": "ReadAsync 403", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439914, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439917, "dur": 43, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439963, "dur": 1, "ph": "X", "name": "ProcessMessages 361", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062439966, "dur": 46, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440015, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440017, "dur": 134, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440154, "dur": 1, "ph": "X", "name": "ProcessMessages 762", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440156, "dur": 48, "ph": "X", "name": "ReadAsync 762", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440207, "dur": 1, "ph": "X", "name": "ProcessMessages 1050", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440209, "dur": 42, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440254, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440256, "dur": 39, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440298, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440300, "dur": 38, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440340, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440342, "dur": 40, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440385, "dur": 1, "ph": "X", "name": "ProcessMessages 615", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440387, "dur": 30, "ph": "X", "name": "ReadAsync 615", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440421, "dur": 41, "ph": "X", "name": "ReadAsync 95", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440465, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440468, "dur": 47, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440518, "dur": 1, "ph": "X", "name": "ProcessMessages 606", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440520, "dur": 33, "ph": "X", "name": "ReadAsync 606", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440556, "dur": 2, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440559, "dur": 43, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440604, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440606, "dur": 31, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440639, "dur": 1, "ph": "X", "name": "ProcessMessages 337", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440641, "dur": 35, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440678, "dur": 1, "ph": "X", "name": "ProcessMessages 447", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440679, "dur": 35, "ph": "X", "name": "ReadAsync 447", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440718, "dur": 38, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440759, "dur": 3, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440764, "dur": 47, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440814, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440816, "dur": 34, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440852, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440855, "dur": 39, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440896, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440899, "dur": 31, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440934, "dur": 36, "ph": "X", "name": "ReadAsync 304", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062440973, "dur": 47, "ph": "X", "name": "ReadAsync 179", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441023, "dur": 1, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441026, "dur": 43, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441072, "dur": 2, "ph": "X", "name": "ProcessMessages 528", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441075, "dur": 42, "ph": "X", "name": "ReadAsync 528", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441120, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441122, "dur": 40, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441165, "dur": 1, "ph": "X", "name": "ProcessMessages 457", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441167, "dur": 38, "ph": "X", "name": "ReadAsync 457", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441207, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441209, "dur": 36, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441249, "dur": 31, "ph": "X", "name": "ReadAsync 387", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441282, "dur": 1, "ph": "X", "name": "ProcessMessages 340", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441284, "dur": 31, "ph": "X", "name": "ReadAsync 340", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441319, "dur": 35, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441356, "dur": 1, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441358, "dur": 44, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441405, "dur": 1, "ph": "X", "name": "ProcessMessages 382", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441406, "dur": 41, "ph": "X", "name": "ReadAsync 382", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441450, "dur": 1, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441453, "dur": 46, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441502, "dur": 2, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441505, "dur": 43, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441551, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441553, "dur": 37, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441593, "dur": 1, "ph": "X", "name": "ProcessMessages 81", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441595, "dur": 48, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441645, "dur": 1, "ph": "X", "name": "ProcessMessages 908", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441648, "dur": 47, "ph": "X", "name": "ReadAsync 908", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441698, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441701, "dur": 43, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441747, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441749, "dur": 44, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441796, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441798, "dur": 43, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441844, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441846, "dur": 42, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441891, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441893, "dur": 47, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441945, "dur": 2, "ph": "X", "name": "ProcessMessages 518", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062441949, "dur": 53, "ph": "X", "name": "ReadAsync 518", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442007, "dur": 3, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442012, "dur": 44, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442058, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442061, "dur": 47, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442110, "dur": 1, "ph": "X", "name": "ProcessMessages 632", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442113, "dur": 46, "ph": "X", "name": "ReadAsync 632", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442161, "dur": 1, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442163, "dur": 37, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442202, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442204, "dur": 38, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442245, "dur": 1, "ph": "X", "name": "ProcessMessages 346", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442247, "dur": 35, "ph": "X", "name": "ReadAsync 346", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442287, "dur": 42, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442332, "dur": 1, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442334, "dur": 37, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442375, "dur": 1, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442377, "dur": 42, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442421, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442422, "dur": 38, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442463, "dur": 1, "ph": "X", "name": "ProcessMessages 421", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442465, "dur": 40, "ph": "X", "name": "ReadAsync 421", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442507, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442509, "dur": 46, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442557, "dur": 1, "ph": "X", "name": "ProcessMessages 396", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442560, "dur": 45, "ph": "X", "name": "ReadAsync 396", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442608, "dur": 1, "ph": "X", "name": "ProcessMessages 610", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442611, "dur": 41, "ph": "X", "name": "ReadAsync 610", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442655, "dur": 1, "ph": "X", "name": "ProcessMessages 506", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442657, "dur": 36, "ph": "X", "name": "ReadAsync 506", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442695, "dur": 1, "ph": "X", "name": "ProcessMessages 331", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442697, "dur": 31, "ph": "X", "name": "ReadAsync 331", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442731, "dur": 34, "ph": "X", "name": "ReadAsync 277", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442769, "dur": 31, "ph": "X", "name": "ReadAsync 338", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442804, "dur": 33, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442841, "dur": 1, "ph": "X", "name": "ProcessMessages 265", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442843, "dur": 48, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442894, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442896, "dur": 38, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442937, "dur": 1, "ph": "X", "name": "ProcessMessages 344", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442940, "dur": 44, "ph": "X", "name": "ReadAsync 344", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442987, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062442990, "dur": 38, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443032, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443034, "dur": 40, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443078, "dur": 37, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443120, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443163, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443165, "dur": 45, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443213, "dur": 1, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443215, "dur": 32, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443251, "dur": 78, "ph": "X", "name": "ReadAsync 136", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443333, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443371, "dur": 1, "ph": "X", "name": "ProcessMessages 423", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443373, "dur": 36, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443410, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443412, "dur": 36, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443450, "dur": 1, "ph": "X", "name": "ProcessMessages 248", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443453, "dur": 83, "ph": "X", "name": "ReadAsync 248", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443540, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443583, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443586, "dur": 37, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443626, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443628, "dur": 31, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443662, "dur": 92, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443756, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443758, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443803, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443806, "dur": 37, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443845, "dur": 1, "ph": "X", "name": "ProcessMessages 520", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443847, "dur": 80, "ph": "X", "name": "ReadAsync 520", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443930, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443970, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062443972, "dur": 54, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444029, "dur": 1, "ph": "X", "name": "ProcessMessages 456", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444032, "dur": 42, "ph": "X", "name": "ReadAsync 456", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444077, "dur": 1, "ph": "X", "name": "ProcessMessages 146", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444079, "dur": 74, "ph": "X", "name": "ReadAsync 146", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444158, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444202, "dur": 1, "ph": "X", "name": "ProcessMessages 442", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444204, "dur": 36, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444242, "dur": 1, "ph": "X", "name": "ProcessMessages 509", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444244, "dur": 34, "ph": "X", "name": "ReadAsync 509", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444281, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444284, "dur": 67, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444355, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444397, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444401, "dur": 38, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444440, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444442, "dur": 28, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444473, "dur": 70, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444547, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444602, "dur": 1, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444604, "dur": 45, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444652, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444654, "dur": 32, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444689, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444691, "dur": 56, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444751, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444789, "dur": 1, "ph": "X", "name": "ProcessMessages 413", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444790, "dur": 36, "ph": "X", "name": "ReadAsync 413", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444828, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444830, "dur": 34, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444867, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444871, "dur": 77, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062444953, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445005, "dur": 2, "ph": "X", "name": "ProcessMessages 493", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445008, "dur": 46, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445057, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445059, "dur": 74, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445138, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445181, "dur": 1, "ph": "X", "name": "ProcessMessages 532", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445184, "dur": 47, "ph": "X", "name": "ReadAsync 532", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445234, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445236, "dur": 34, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445274, "dur": 58, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445337, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445379, "dur": 1, "ph": "X", "name": "ProcessMessages 604", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445381, "dur": 42, "ph": "X", "name": "ReadAsync 604", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445426, "dur": 1, "ph": "X", "name": "ProcessMessages 679", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445428, "dur": 31, "ph": "X", "name": "ReadAsync 679", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445461, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445464, "dur": 66, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445535, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445579, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445581, "dur": 40, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445623, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445626, "dur": 31, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445660, "dur": 1, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445663, "dur": 63, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445729, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445731, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445780, "dur": 2, "ph": "X", "name": "ProcessMessages 556", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445783, "dur": 49, "ph": "X", "name": "ReadAsync 556", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445835, "dur": 1, "ph": "X", "name": "ProcessMessages 638", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445838, "dur": 74, "ph": "X", "name": "ReadAsync 638", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445915, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445916, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445958, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062445960, "dur": 43, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446005, "dur": 3, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446009, "dur": 42, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446054, "dur": 1, "ph": "X", "name": "ProcessMessages 309", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446057, "dur": 70, "ph": "X", "name": "ReadAsync 309", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446132, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446182, "dur": 1, "ph": "X", "name": "ProcessMessages 597", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446184, "dur": 37, "ph": "X", "name": "ReadAsync 597", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446223, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446225, "dur": 78, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446307, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446345, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446347, "dur": 35, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446385, "dur": 39, "ph": "X", "name": "ReadAsync 361", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446427, "dur": 69, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446499, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446537, "dur": 1, "ph": "X", "name": "ProcessMessages 401", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446539, "dur": 34, "ph": "X", "name": "ReadAsync 401", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446577, "dur": 32, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446612, "dur": 92, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446707, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446746, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446748, "dur": 34, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446785, "dur": 1, "ph": "X", "name": "ProcessMessages 464", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446786, "dur": 30, "ph": "X", "name": "ReadAsync 464", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446818, "dur": 1, "ph": "X", "name": "ProcessMessages 145", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446820, "dur": 73, "ph": "X", "name": "ReadAsync 145", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446896, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446935, "dur": 1, "ph": "X", "name": "ProcessMessages 411", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446936, "dur": 38, "ph": "X", "name": "ReadAsync 411", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446976, "dur": 1, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062446979, "dur": 46, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447028, "dur": 1, "ph": "X", "name": "ProcessMessages 463", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447030, "dur": 42, "ph": "X", "name": "ReadAsync 463", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447075, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447076, "dur": 64, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447146, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447189, "dur": 1, "ph": "X", "name": "ProcessMessages 454", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447191, "dur": 38, "ph": "X", "name": "ReadAsync 454", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447231, "dur": 1, "ph": "X", "name": "ProcessMessages 558", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447233, "dur": 36, "ph": "X", "name": "ReadAsync 558", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447271, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447272, "dur": 35, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447309, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447311, "dur": 37, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447351, "dur": 32, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447386, "dur": 30, "ph": "X", "name": "ReadAsync 231", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447419, "dur": 31, "ph": "X", "name": "ReadAsync 82", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447453, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447454, "dur": 74, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447531, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447572, "dur": 2, "ph": "X", "name": "ProcessMessages 443", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447575, "dur": 46, "ph": "X", "name": "ReadAsync 443", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447624, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447627, "dur": 34, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447663, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447665, "dur": 63, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447731, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447771, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447773, "dur": 37, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447815, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447817, "dur": 41, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447861, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447863, "dur": 63, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447929, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447969, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062447971, "dur": 53, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448028, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448031, "dur": 38, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448073, "dur": 1, "ph": "X", "name": "ProcessMessages 134", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448075, "dur": 61, "ph": "X", "name": "ReadAsync 134", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448140, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448181, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448184, "dur": 51, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448237, "dur": 1, "ph": "X", "name": "ProcessMessages 600", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448239, "dur": 39, "ph": "X", "name": "ReadAsync 600", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448280, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448282, "dur": 36, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448320, "dur": 1, "ph": "X", "name": "ProcessMessages 446", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448321, "dur": 36, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448359, "dur": 1, "ph": "X", "name": "ProcessMessages 363", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448361, "dur": 36, "ph": "X", "name": "ReadAsync 363", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448399, "dur": 1, "ph": "X", "name": "ProcessMessages 321", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448401, "dur": 32, "ph": "X", "name": "ReadAsync 321", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448436, "dur": 86, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448527, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448565, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448567, "dur": 47, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448616, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448618, "dur": 34, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448655, "dur": 75, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448734, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448737, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448787, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448789, "dur": 36, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448827, "dur": 1, "ph": "X", "name": "ProcessMessages 356", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448829, "dur": 31, "ph": "X", "name": "ReadAsync 356", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448863, "dur": 80, "ph": "X", "name": "ReadAsync 228", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448948, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448993, "dur": 1, "ph": "X", "name": "ProcessMessages 514", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062448996, "dur": 49, "ph": "X", "name": "ReadAsync 514", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449047, "dur": 1, "ph": "X", "name": "ProcessMessages 725", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449049, "dur": 43, "ph": "X", "name": "ReadAsync 725", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449096, "dur": 3, "ph": "X", "name": "ProcessMessages 578", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449100, "dur": 48, "ph": "X", "name": "ReadAsync 578", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449151, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449153, "dur": 37, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449193, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449194, "dur": 31, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449227, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449229, "dur": 89, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449322, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449367, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449369, "dur": 37, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449408, "dur": 1, "ph": "X", "name": "ProcessMessages 372", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449410, "dur": 32, "ph": "X", "name": "ReadAsync 372", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449445, "dur": 89, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449539, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449585, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449587, "dur": 41, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449630, "dur": 1, "ph": "X", "name": "ProcessMessages 570", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449632, "dur": 33, "ph": "X", "name": "ReadAsync 570", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449668, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449671, "dur": 76, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449749, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449751, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449802, "dur": 1, "ph": "X", "name": "ProcessMessages 469", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449804, "dur": 44, "ph": "X", "name": "ReadAsync 469", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449851, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449854, "dur": 95, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062449953, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450005, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450009, "dur": 48, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450061, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450063, "dur": 41, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450107, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450113, "dur": 70, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450187, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450189, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450245, "dur": 2, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450249, "dur": 54, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450306, "dur": 2, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450309, "dur": 52, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450365, "dur": 2, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450368, "dur": 69, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450443, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450493, "dur": 1, "ph": "X", "name": "ProcessMessages 502", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450496, "dur": 43, "ph": "X", "name": "ReadAsync 502", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450542, "dur": 1, "ph": "X", "name": "ProcessMessages 633", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450544, "dur": 76, "ph": "X", "name": "ReadAsync 633", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450623, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450676, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450679, "dur": 46, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450728, "dur": 1, "ph": "X", "name": "ProcessMessages 649", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450730, "dur": 72, "ph": "X", "name": "ReadAsync 649", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450806, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450848, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450850, "dur": 45, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450899, "dur": 1, "ph": "X", "name": "ProcessMessages 645", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450902, "dur": 84, "ph": "X", "name": "ReadAsync 645", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450989, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062450991, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451039, "dur": 1, "ph": "X", "name": "ProcessMessages 541", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451042, "dur": 45, "ph": "X", "name": "ReadAsync 541", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451091, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451094, "dur": 63, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451161, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451206, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451208, "dur": 39, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451250, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451252, "dur": 31, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451287, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451289, "dur": 72, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451365, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451410, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451413, "dur": 40, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451455, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451457, "dur": 85, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451546, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451589, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451591, "dur": 39, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451633, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451635, "dur": 31, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451669, "dur": 1, "ph": "X", "name": "ProcessMessages 58", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451671, "dur": 65, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451740, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451777, "dur": 1, "ph": "X", "name": "ProcessMessages 325", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451780, "dur": 39, "ph": "X", "name": "ReadAsync 325", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451822, "dur": 1, "ph": "X", "name": "ProcessMessages 510", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451824, "dur": 35, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451861, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451863, "dur": 79, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451946, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451992, "dur": 2, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062451995, "dur": 46, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452044, "dur": 1, "ph": "X", "name": "ProcessMessages 669", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452047, "dur": 74, "ph": "X", "name": "ReadAsync 669", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452126, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452175, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452177, "dur": 39, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452219, "dur": 1, "ph": "X", "name": "ProcessMessages 559", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452222, "dur": 77, "ph": "X", "name": "ReadAsync 559", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452303, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452346, "dur": 1, "ph": "X", "name": "ProcessMessages 499", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452348, "dur": 41, "ph": "X", "name": "ReadAsync 499", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452392, "dur": 1, "ph": "X", "name": "ProcessMessages 609", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452394, "dur": 30, "ph": "X", "name": "ReadAsync 609", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452426, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452428, "dur": 71, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452504, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452546, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452548, "dur": 41, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452592, "dur": 1, "ph": "X", "name": "ProcessMessages 587", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452595, "dur": 30, "ph": "X", "name": "ReadAsync 587", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452628, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452630, "dur": 62, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452695, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452738, "dur": 1, "ph": "X", "name": "ProcessMessages 508", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452740, "dur": 38, "ph": "X", "name": "ReadAsync 508", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452780, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452782, "dur": 31, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452816, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452818, "dur": 64, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452887, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452931, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452934, "dur": 40, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452977, "dur": 1, "ph": "X", "name": "ProcessMessages 640", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062452980, "dur": 37, "ph": "X", "name": "ReadAsync 640", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453019, "dur": 1, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453022, "dur": 56, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453081, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453127, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453130, "dur": 38, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453171, "dur": 1, "ph": "X", "name": "ProcessMessages 662", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453175, "dur": 91, "ph": "X", "name": "ReadAsync 662", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453270, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453312, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453314, "dur": 39, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453356, "dur": 1, "ph": "X", "name": "ProcessMessages 577", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453358, "dur": 30, "ph": "X", "name": "ReadAsync 577", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453391, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453393, "dur": 61, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453458, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453498, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453500, "dur": 37, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453540, "dur": 1, "ph": "X", "name": "ProcessMessages 651", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453542, "dur": 73, "ph": "X", "name": "ReadAsync 651", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453620, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453660, "dur": 1, "ph": "X", "name": "ProcessMessages 574", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453663, "dur": 39, "ph": "X", "name": "ReadAsync 574", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453705, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453707, "dur": 31, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453741, "dur": 1, "ph": "X", "name": "ProcessMessages 57", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453742, "dur": 65, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453812, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453854, "dur": 1, "ph": "X", "name": "ProcessMessages 526", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453856, "dur": 38, "ph": "X", "name": "ReadAsync 526", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453897, "dur": 1, "ph": "X", "name": "ProcessMessages 362", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453899, "dur": 33, "ph": "X", "name": "ReadAsync 362", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453934, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062453936, "dur": 80, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454021, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454023, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454075, "dur": 1, "ph": "X", "name": "ProcessMessages 567", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454078, "dur": 39, "ph": "X", "name": "ReadAsync 567", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454120, "dur": 1, "ph": "X", "name": "ProcessMessages 460", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454122, "dur": 72, "ph": "X", "name": "ReadAsync 460", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454198, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454240, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454242, "dur": 38, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454283, "dur": 1, "ph": "X", "name": "ProcessMessages 576", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454286, "dur": 31, "ph": "X", "name": "ReadAsync 576", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454319, "dur": 2, "ph": "X", "name": "ProcessMessages 56", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454322, "dur": 74, "ph": "X", "name": "ReadAsync 56", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454398, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454400, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454442, "dur": 2, "ph": "X", "name": "ProcessMessages 547", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454445, "dur": 40, "ph": "X", "name": "ReadAsync 547", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454488, "dur": 1, "ph": "X", "name": "ProcessMessages 583", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454490, "dur": 32, "ph": "X", "name": "ReadAsync 583", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454526, "dur": 1, "ph": "X", "name": "ProcessMessages 62", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454529, "dur": 61, "ph": "X", "name": "ReadAsync 62", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454594, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454638, "dur": 1, "ph": "X", "name": "ProcessMessages 511", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454640, "dur": 34, "ph": "X", "name": "ReadAsync 511", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454676, "dur": 1, "ph": "X", "name": "ProcessMessages 360", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454679, "dur": 32, "ph": "X", "name": "ReadAsync 360", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454714, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454716, "dur": 85, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454804, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454807, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454861, "dur": 2, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454865, "dur": 48, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454918, "dur": 1, "ph": "X", "name": "ProcessMessages 647", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454920, "dur": 41, "ph": "X", "name": "ReadAsync 647", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454963, "dur": 1, "ph": "X", "name": "ProcessMessages 582", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062454965, "dur": 46, "ph": "X", "name": "ReadAsync 582", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455014, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455017, "dur": 42, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455063, "dur": 3, "ph": "X", "name": "ProcessMessages 320", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455068, "dur": 38, "ph": "X", "name": "ReadAsync 320", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455109, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455111, "dur": 64, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455180, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455222, "dur": 1, "ph": "X", "name": "ProcessMessages 424", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455224, "dur": 39, "ph": "X", "name": "ReadAsync 424", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455266, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455268, "dur": 38, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455309, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455311, "dur": 39, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455353, "dur": 1, "ph": "X", "name": "ProcessMessages 453", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455356, "dur": 38, "ph": "X", "name": "ReadAsync 453", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455397, "dur": 1, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455400, "dur": 33, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455435, "dur": 1, "ph": "X", "name": "ProcessMessages 152", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455437, "dur": 35, "ph": "X", "name": "ReadAsync 152", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455475, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455477, "dur": 85, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455566, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455609, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455612, "dur": 42, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455656, "dur": 1, "ph": "X", "name": "ProcessMessages 549", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455659, "dur": 42, "ph": "X", "name": "ReadAsync 549", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455704, "dur": 2, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455708, "dur": 48, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455759, "dur": 1, "ph": "X", "name": "ProcessMessages 475", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455762, "dur": 59, "ph": "X", "name": "ReadAsync 475", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455823, "dur": 1, "ph": "X", "name": "ProcessMessages 560", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455826, "dur": 40, "ph": "X", "name": "ReadAsync 560", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455870, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455874, "dur": 104, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455982, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062455984, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456026, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456030, "dur": 99, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456132, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456135, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456172, "dur": 400, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456576, "dur": 76, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456655, "dur": 9, "ph": "X", "name": "ProcessMessages 1024", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456666, "dur": 41, "ph": "X", "name": "ReadAsync 1024", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456711, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456714, "dur": 43, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456760, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456763, "dur": 42, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456808, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456811, "dur": 35, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456849, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456851, "dur": 24, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456877, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456879, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456912, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456915, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456952, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062456955, "dur": 506, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457465, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457468, "dur": 92, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457564, "dur": 9, "ph": "X", "name": "ProcessMessages 944", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457574, "dur": 32, "ph": "X", "name": "ReadAsync 944", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457609, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457612, "dur": 46, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457662, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457665, "dur": 38, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457707, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457709, "dur": 33, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457746, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457750, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457786, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457789, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457826, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457829, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457866, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457869, "dur": 33, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457905, "dur": 3, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457910, "dur": 38, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457953, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457956, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457994, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062457997, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458039, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458043, "dur": 39, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458085, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458088, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458130, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458132, "dur": 33, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458168, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458170, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458208, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458210, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458245, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458248, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458282, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458285, "dur": 30, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458318, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458320, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458355, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458357, "dur": 31, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458391, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458394, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458430, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458432, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458470, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458473, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458510, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458513, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458545, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458547, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458583, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458585, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458619, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458622, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458655, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458657, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458692, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458695, "dur": 33, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458731, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458734, "dur": 34, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458770, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458774, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458808, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458811, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458845, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458847, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458886, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458889, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458928, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062458930, "dur": 69, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459002, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459006, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459040, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459043, "dur": 30, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459076, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459079, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459112, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459115, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459151, "dur": 3, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459155, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459190, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459194, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459233, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459236, "dur": 39, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459278, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459281, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459326, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459330, "dur": 41, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459373, "dur": 1, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459376, "dur": 152, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459532, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459534, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459577, "dur": 2, "ph": "X", "name": "ProcessMessages 132", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459580, "dur": 50, "ph": "X", "name": "ReadAsync 132", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459634, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459637, "dur": 37, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459677, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459680, "dur": 37, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459721, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459724, "dur": 46, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459773, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459777, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459820, "dur": 2, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459823, "dur": 32, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459858, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459861, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459899, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459901, "dur": 33, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459937, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062459940, "dur": 66, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460010, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460013, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460053, "dur": 2, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460056, "dur": 49, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460109, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460112, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460152, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460155, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460194, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460197, "dur": 34, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460235, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460238, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460280, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460283, "dur": 58, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460344, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460350, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460394, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460396, "dur": 38, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460438, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460440, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460478, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460481, "dur": 48, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460532, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460534, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460572, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460576, "dur": 34, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460613, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460615, "dur": 34, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460653, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460655, "dur": 38, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460698, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460701, "dur": 36, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460741, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460744, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460783, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460786, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460824, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460827, "dur": 32, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460863, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460865, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460910, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460913, "dur": 34, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460951, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460955, "dur": 38, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062460997, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461000, "dur": 53, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461056, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461059, "dur": 37, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461099, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461103, "dur": 38, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461144, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461147, "dur": 32, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461182, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461184, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461219, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461221, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461255, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461257, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461293, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461295, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461329, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461332, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461369, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461371, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461404, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461406, "dur": 89, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461498, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461500, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461532, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461534, "dur": 128, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461667, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461707, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062461710, "dur": 6052, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062467769, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062467773, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062467832, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062467836, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062467878, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062467881, "dur": 195, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062468079, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062468082, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062468117, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062468119, "dur": 999, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469123, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469125, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469164, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469167, "dur": 186, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469357, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469359, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469397, "dur": 2, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469400, "dur": 120, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469523, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469525, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469561, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469563, "dur": 71, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469637, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469639, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469672, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469674, "dur": 49, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469728, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469760, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469762, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469797, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062469800, "dur": 240, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062470043, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062470046, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062470105, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062470108, "dur": 242, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062470353, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062470356, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062470393, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062470396, "dur": 47, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062470446, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062470448, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062470497, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062470500, "dur": 500, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471004, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471006, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471048, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471051, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471090, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471092, "dur": 89, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471186, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471188, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471227, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471230, "dur": 33, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471267, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471269, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471305, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471308, "dur": 29, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471340, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471342, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471405, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471407, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471441, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471443, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471475, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471477, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471510, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471513, "dur": 99, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471615, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471618, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471652, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471655, "dur": 33, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471692, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471694, "dur": 140, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471838, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471840, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471870, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471872, "dur": 55, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471931, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062471934, "dur": 63, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472001, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472003, "dur": 48, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472055, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472058, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472096, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472098, "dur": 57, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472158, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472161, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472197, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472199, "dur": 50, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472253, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472255, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472290, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472293, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472327, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472330, "dur": 29, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472361, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472363, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472404, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472406, "dur": 221, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472630, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472632, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472666, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472668, "dur": 161, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472832, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472834, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472871, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062472874, "dur": 202, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473079, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473082, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473118, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473121, "dur": 35, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473160, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473162, "dur": 57, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473224, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473261, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473264, "dur": 41, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473309, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473312, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473352, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473354, "dur": 60, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473418, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473421, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473458, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473460, "dur": 153, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473617, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473620, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473654, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473656, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473690, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473693, "dur": 163, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473861, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473897, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473900, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473935, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062473937, "dur": 59, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062474000, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062474002, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062474040, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062474042, "dur": 567, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062474613, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062474616, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062474658, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062474661, "dur": 99, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062474764, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062474768, "dur": 52, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062474824, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062474827, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062474860, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062474863, "dur": 154, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475021, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475023, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475062, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475074, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475113, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475116, "dur": 46, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475166, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475168, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475218, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475220, "dur": 175, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475399, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475401, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475440, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475442, "dur": 253, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475701, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475704, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475745, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475748, "dur": 37, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475790, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062475792, "dur": 322, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476117, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476119, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476153, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476156, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476194, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476196, "dur": 28, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476228, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476230, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476265, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476267, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476301, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476304, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476338, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476340, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476374, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476376, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476411, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476414, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476448, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476451, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476487, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476490, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476527, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476530, "dur": 366, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476899, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476901, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476943, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062476946, "dur": 56, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062477006, "dur": 2, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062477010, "dur": 332, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062477345, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062477347, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062477385, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062477389, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062477447, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062477450, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062477487, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062477489, "dur": 114, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062477606, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062477610, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062477646, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062477649, "dur": 186, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062477838, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062477840, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062477874, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062477877, "dur": 508, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062478389, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062478391, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062478431, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062478434, "dur": 291, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062478729, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062478731, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062478772, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062478775, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062478813, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062478816, "dur": 132, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062478951, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062478954, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062478998, "dur": 706, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062479708, "dur": 47, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062479758, "dur": 3, "ph": "X", "name": "ProcessMessages 128", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062479762, "dur": 385, "ph": "X", "name": "ReadAsync 128", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062480151, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062480154, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062480195, "dur": 3, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062480199, "dur": 137, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062480340, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062480343, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062480383, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062480385, "dur": 218, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062480607, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062480610, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062480652, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062480655, "dur": 166, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062480825, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062480827, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062480867, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062480869, "dur": 30, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062480903, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062480907, "dur": 131, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481042, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481044, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481084, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481088, "dur": 293, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481384, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481386, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481423, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481426, "dur": 39, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481468, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481471, "dur": 49, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481523, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481526, "dur": 105, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481635, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481638, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481680, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481682, "dur": 35, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481721, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481723, "dur": 143, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481870, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481873, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481908, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062481910, "dur": 430, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482344, "dur": 8, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482355, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482397, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482400, "dur": 96, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482508, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482511, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482553, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482555, "dur": 97, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482656, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482659, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482702, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482705, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482742, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482745, "dur": 92, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482841, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482845, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482882, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482885, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482923, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482925, "dur": 67, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062482997, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483000, "dur": 98, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483103, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483107, "dur": 130, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483241, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483243, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483294, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483296, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483330, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483333, "dur": 97, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483433, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483435, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483472, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483475, "dur": 52, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483530, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483532, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483571, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483573, "dur": 324, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483902, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483904, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483947, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062483949, "dur": 153, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484106, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484110, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484149, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484151, "dur": 78, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484233, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484236, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484275, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484278, "dur": 211, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484493, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484496, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484538, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484541, "dur": 54, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484599, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484601, "dur": 145, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484753, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484791, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484794, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484832, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484834, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484880, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484883, "dur": 32, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484918, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062484921, "dur": 172, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062485095, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062485098, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062485136, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062485139, "dur": 58, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062485206, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062485256, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062485259, "dur": 37, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062485299, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062485301, "dur": 37, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062485342, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062485344, "dur": 76, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062485425, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062485466, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062485468, "dur": 387, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062485859, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062485861, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062485917, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062485920, "dur": 302, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062486226, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062486228, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062486279, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062486281, "dur": 141, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062486426, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062486429, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062486481, "dur": 39, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062486524, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062486526, "dur": 523, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062487053, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062487055, "dur": 65, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062487124, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062487126, "dur": 1114, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062488245, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062488248, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062488293, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062488297, "dur": 108518, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062596823, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062596828, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062596862, "dur": 1641, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062598510, "dur": 2914, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062601431, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062601435, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062601479, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062601482, "dur": 51, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062601536, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062601539, "dur": 58, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062601602, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062601647, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062601650, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062601683, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062601686, "dur": 44, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062601733, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062601736, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062601773, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062601776, "dur": 745, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062602525, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062602527, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062602562, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062602565, "dur": 430, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062602998, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062603001, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062603039, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062603042, "dur": 849, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062603895, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062603898, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062603932, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062603935, "dur": 115, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062604052, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062604055, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062604106, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062604110, "dur": 35, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062604147, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062604150, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062604182, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062604184, "dur": 78, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062604267, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062604269, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062604313, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062604315, "dur": 792, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062605110, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062605113, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062605136, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062605138, "dur": 606, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062605749, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062605751, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062605804, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062605806, "dur": 325, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062606135, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062606138, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062606173, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062606175, "dur": 343, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062606523, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062606556, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062606559, "dur": 241, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062606804, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062606837, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062606839, "dur": 29, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062606871, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062606873, "dur": 161, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062607037, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062607039, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062607074, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062607076, "dur": 566, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062607645, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062607647, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062607684, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062607686, "dur": 740, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062608430, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062608432, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062608466, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062608468, "dur": 136, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062608609, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062608644, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062608646, "dur": 225, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062608876, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062608907, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062608909, "dur": 401, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062609315, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062609318, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062609351, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062609353, "dur": 284, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062609640, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062609642, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062609675, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062609677, "dur": 349, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062610029, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062610031, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062610064, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062610066, "dur": 294, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062610364, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062610366, "dur": 24, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062610392, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062610394, "dur": 183, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062610581, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062610583, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062610619, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062610621, "dur": 811, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062611436, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062611439, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062611491, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062611494, "dur": 73, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062611571, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062611573, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062611609, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062611612, "dur": 252, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062611869, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062611871, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062611906, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062611908, "dur": 861, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062612774, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062612776, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062612813, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062612815, "dur": 32, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062612850, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062612852, "dur": 269, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062613124, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062613127, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062613161, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062613163, "dur": 704, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062613872, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062613874, "dur": 30, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062613907, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062613909, "dur": 89, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062614002, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062614004, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062614036, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062614038, "dur": 243, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062614285, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062614287, "dur": 29, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062614319, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062614322, "dur": 794, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062615119, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062615122, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062615159, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062615162, "dur": 137, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062615302, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062615305, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062615339, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062615341, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062615435, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062615437, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062615485, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062615488, "dur": 653, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062616144, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062616147, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062616182, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062616185, "dur": 283, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062616472, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062616474, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062616509, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062616512, "dur": 55, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062616570, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062616573, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062616610, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062616613, "dur": 922, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062617539, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062617542, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062617578, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062617580, "dur": 88, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062617672, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062617674, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062617710, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062617712, "dur": 389, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062618105, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062618108, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062618142, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062618144, "dur": 726, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062618874, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062618877, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062618911, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062618913, "dur": 238, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062619154, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062619156, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062619193, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062619195, "dur": 295, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062619494, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062619497, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062619532, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062619534, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062619568, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062619570, "dur": 303, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062619877, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062619879, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062619913, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062619915, "dur": 60, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062619979, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062619981, "dur": 43, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620027, "dur": 2, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620030, "dur": 128, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620162, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620164, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620205, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620207, "dur": 34, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620244, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620247, "dur": 33, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620282, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620285, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620328, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620331, "dur": 33, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620368, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620370, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620406, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620408, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620443, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620446, "dur": 48, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620497, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620500, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620536, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620538, "dur": 36, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620578, "dur": 1, "ph": "X", "name": "ProcessMessages 44", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620581, "dur": 42, "ph": "X", "name": "ReadAsync 44", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620627, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620630, "dur": 37, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620671, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620674, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620714, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620717, "dur": 40, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620762, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620765, "dur": 39, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620807, "dur": 1, "ph": "X", "name": "ProcessMessages 92", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620810, "dur": 35, "ph": "X", "name": "ReadAsync 92", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620849, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620851, "dur": 32, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620887, "dur": 1, "ph": "X", "name": "ProcessMessages 45", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620912, "dur": 68, "ph": "X", "name": "ReadAsync 45", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620984, "dur": 4, "ph": "X", "name": "ProcessMessages 147", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062620989, "dur": 42, "ph": "X", "name": "ReadAsync 147", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621036, "dur": 2, "ph": "X", "name": "ProcessMessages 156", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621041, "dur": 37, "ph": "X", "name": "ReadAsync 156", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621081, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621084, "dur": 35, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621123, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621126, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621167, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621170, "dur": 41, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621214, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621216, "dur": 44, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621264, "dur": 2, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621267, "dur": 44, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621315, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621318, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621362, "dur": 2, "ph": "X", "name": "ProcessMessages 116", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621366, "dur": 116, "ph": "X", "name": "ReadAsync 116", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621486, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621488, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621536, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621538, "dur": 168, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621710, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621712, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621754, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408062621756, "dur": 7999472, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408070621237, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408070621242, "dur": 54, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408070621299, "dur": 23, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408070621324, "dur": 16572, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408070637907, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408070637912, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408070637962, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408070637966, "dur": 32422, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408070670398, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408070670404, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408070670449, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408070670453, "dur": 2226367, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072896830, "dur": 7, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072896840, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072896891, "dur": 24, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072896916, "dur": 7516, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072904442, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072904447, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072904501, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072904506, "dur": 1712, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072906226, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072906230, "dur": 66, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072906310, "dur": 29, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072906341, "dur": 32070, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072938421, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072938426, "dur": 98, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072938528, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072938532, "dur": 1910, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072940454, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072940471, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072940518, "dur": 28, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408072940548, "dur": 1021336, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408073961904, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408073961908, "dur": 52, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408073961963, "dur": 24, "ph": "X", "name": "ProcessMessages 333", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408073961989, "dur": 15198, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408073977194, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408073977198, "dur": 176, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408073977378, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408073977382, "dur": 93884, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408074071274, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408074071277, "dur": 115, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408074071397, "dur": 17, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408074071415, "dur": 7027, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408074078449, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408074078453, "dur": 81, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408074078537, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408074078540, "dur": 43, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408074078588, "dur": 11, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408074078601, "dur": 2756, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408074081360, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408074081363, "dur": 51, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408074081418, "dur": 40, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408074081476, "dur": 589, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408074082068, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408074082071, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408074082117, "dur": 331, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 6348, "tid": 12884901888, "ts": 1751408074082452, "dur": 8968, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 6348, "tid": 658, "ts": 1751408074110016, "dur": 2729, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 6348, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 6348, "tid": 8589934592, "ts": 1751408062411977, "dur": 146930, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 6348, "tid": 8589934592, "ts": 1751408062558911, "dur": 30, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 6348, "tid": 8589934592, "ts": 1751408062558944, "dur": 2141, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 6348, "tid": 658, "ts": 1751408074112747, "dur": 6, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 6348, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 6348, "tid": 4294967296, "ts": 1751408062387756, "dur": 11705391, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 6348, "tid": 4294967296, "ts": 1751408062392308, "dur": 8564, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 6348, "tid": 4294967296, "ts": 1751408074093450, "dur": 5645, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 6348, "tid": 4294967296, "ts": 1751408074097419, "dur": 121, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 6348, "tid": 4294967296, "ts": 1751408074099174, "dur": 13, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 6348, "tid": 658, "ts": 1751408074112755, "dur": 7, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751408062417352, "dur": 2181, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751408062419547, "dur": 1012, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751408062420714, "dur": 91, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751408062420806, "dur": 365, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751408062422545, "dur": 2473, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751408062426323, "dur": 2596, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751408062428967, "dur": 83, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751408062429091, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751408062429997, "dur": 69, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751408062430175, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751408062430996, "dur": 82, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751408062438849, "dur": 56, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751408062421197, "dur": 34625, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751408062455838, "dur": 11625416, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751408074081256, "dur": 189, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751408074081445, "dur": 82, "ph": "X", "name": "ThreadStateDestroy", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751408074081527, "dur": 63, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751408074081745, "dur": 88, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751408074081883, "dur": 1780, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751408062421521, "dur": 34333, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062455884, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062456094, "dur": 362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_AAC1DDBB583EF4F7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751408062456467, "dur": 171, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EditorToolbarModule.dll_AAC1DDBB583EF4F7.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751408062456644, "dur": 64, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.MarshallingModule.dll_EABB3BB0B4DAF932.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751408062457338, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751408062457535, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_7D2E9826DFBFBE48.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751408062457619, "dur": 148, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751408062457775, "dur": 219, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062457995, "dur": 363, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.rsp2"}}, {"pid": 12345, "tid": 1, "ts": 1751408062458437, "dur": 222, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751408062458878, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751408062459114, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751408062459185, "dur": 182, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062459401, "dur": 82, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 1, "ts": 1751408062459640, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751408062459794, "dur": 95, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062459889, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751408062460591, "dur": 154, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/18218310762646611085.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751408062460830, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751408062460911, "dur": 101, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751408062461013, "dur": 594, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062461608, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062461809, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062462024, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062462238, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062462501, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062462723, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062462939, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062463158, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062463387, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062463604, "dur": 562, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062464167, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062464397, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062464633, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062464873, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062465086, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062465304, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062465527, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062465755, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062466044, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062466295, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062466545, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062466766, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062466989, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062467239, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062467507, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062467794, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062468123, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062468331, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062468577, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062468799, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062469043, "dur": 1214, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751408062470280, "dur": 8236, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751408062478628, "dur": 205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751408062478834, "dur": 612, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062479450, "dur": 875, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751408062480326, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062480442, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751408062480653, "dur": 565, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751408062481298, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751408062481506, "dur": 928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751408062482514, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751408062482723, "dur": 566, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751408062483290, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062483386, "dur": 2660, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062486048, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751408062486319, "dur": 112936, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062599259, "dur": 2035, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751408062601339, "dur": 2499, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751408062603876, "dur": 63, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751408062603942, "dur": 2881, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751408062606870, "dur": 2557, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751408062609473, "dur": 2791, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751408062612265, "dur": 355, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062612630, "dur": 2457, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751408062615155, "dur": 2436, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751408062617592, "dur": 328, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062617934, "dur": 2462, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751408062621152, "dur": 168, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751408062621345, "dur": 11459949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062421847, "dur": 34075, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062455926, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_CCD605E26FC2B53B.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751408062456058, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestWWWModule.dll_92A1593FB10DA16D.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751408062456461, "dur": 85, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.DSPGraphModule.dll_11834CDAF2387C83.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751408062456572, "dur": 84, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062456856, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SubsystemsModule.dll_B9448EEF9FF8FB2A.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751408062457336, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751408062457453, "dur": 102, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751408062457579, "dur": 64, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062457887, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751408062457979, "dur": 222, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751408062458676, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751408062458792, "dur": 136, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751408062459162, "dur": 184, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062459445, "dur": 178, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 2, "ts": 1751408062459882, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751408062459980, "dur": 160, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751408062460142, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751408062460265, "dur": 170, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062460741, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751408062460808, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062460880, "dur": 62, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9703144790800738880.rsp"}}, {"pid": 12345, "tid": 2, "ts": 1751408062460943, "dur": 372, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062461316, "dur": 328, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062461645, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062461848, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062462089, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062462335, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062462581, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062462832, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062463039, "dur": 432, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062463471, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062463680, "dur": 564, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062464244, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062464465, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062464693, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062464941, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062465158, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062465409, "dur": 466, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062465876, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062466095, "dur": 329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062466424, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062466656, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062466906, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062467121, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062467360, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062467644, "dur": 753, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Inspection\\Unity\\RectInspector.cs"}}, {"pid": 12345, "tid": 2, "ts": 1751408062467629, "dur": 991, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062468620, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062468871, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062469185, "dur": 254, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751408062469470, "dur": 1502, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751408062471039, "dur": 178, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751408062471240, "dur": 229, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751408062471489, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751408062471695, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751408062472578, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062472665, "dur": 211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751408062472907, "dur": 6154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751408062479113, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062479195, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751408062479398, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751408062480153, "dur": 1141, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062481294, "dur": 1434, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062482729, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751408062482887, "dur": 830, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062483729, "dur": 577, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751408062484377, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751408062484579, "dur": 515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751408062485158, "dur": 892, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062486051, "dur": 74965, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062562513, "dur": 297, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 2, "ts": 1751408062562811, "dur": 1656, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 2, "ts": 1751408062564468, "dur": 175, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 2, "ts": 1751408062561018, "dur": 3630, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062564648, "dur": 34603, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751408062599252, "dur": 2186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751408062601482, "dur": 2396, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751408062603959, "dur": 2334, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751408062606355, "dur": 2303, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751408062608709, "dur": 2515, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751408062611263, "dur": 2391, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751408062613694, "dur": 2677, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751408062616406, "dur": 2536, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751408062618986, "dur": 2281, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751408062621330, "dur": 11459949, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062421604, "dur": 34266, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062455887, "dur": 113, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062456108, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GIModule.dll_31DB0EF841A9843D.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751408062456442, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ContentLoadModule.dll_DB68F65C9F3573CD.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751408062456618, "dur": 190, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_9D5FC13BBE3E7A4F.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751408062456897, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062457302, "dur": 63, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751408062457378, "dur": 98, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751408062457478, "dur": 51, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751408062457547, "dur": 91, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751408062457950, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751408062458039, "dur": 166, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751408062458803, "dur": 100, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751408062459183, "dur": 181, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062459445, "dur": 567, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.rsp2"}}, {"pid": 12345, "tid": 3, "ts": 1751408062460230, "dur": 72, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10678863128556690338.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751408062460304, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062460407, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062460530, "dur": 83, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10411718816959651794.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751408062460615, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12065492119293344149.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751408062460711, "dur": 59, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/10292501669419677951.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751408062460771, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062460890, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/13141106764800131837.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751408062460949, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062461209, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062461416, "dur": 349, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062461765, "dur": 610, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062462375, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062462620, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062462840, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062463078, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062463318, "dur": 844, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Drawing\\Inspector\\PropertyDrawers\\ColorPropertyDrawer.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751408062463290, "dur": 1077, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062464367, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062464592, "dur": 307, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062464899, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062465135, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062465361, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062465579, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062465811, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062466048, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062466276, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062466521, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062466742, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062467129, "dur": 533, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Core\\Widgets\\StickyNote\\StickyNoteOption.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751408062467031, "dur": 948, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062467979, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062468202, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062468428, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062468649, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062468998, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751408062469199, "dur": 2423, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751408062471676, "dur": 79, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062471767, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751408062471990, "dur": 2798, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751408062474912, "dur": 593, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751408062475530, "dur": 1152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751408062476734, "dur": 1128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1751408062477898, "dur": 168, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062478825, "dur": 117820, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 3, "ts": 1751408062599252, "dur": 2121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751408062601375, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062601438, "dur": 2239, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751408062603725, "dur": 2193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751408062605968, "dur": 2062, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751408062608037, "dur": 204, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062608256, "dur": 1990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751408062610247, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062610410, "dur": 2416, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751408062612827, "dur": 115, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062612955, "dur": 2262, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751408062615267, "dur": 2186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751408062617505, "dur": 2306, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751408062619812, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062619863, "dur": 202, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751408062620071, "dur": 136, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408062621091, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Core.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751408062621171, "dur": 8001479, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408070622674, "dur": 43656, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751408070622652, "dur": 45899, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751408070669890, "dur": 253, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751408070670646, "dur": 2225959, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751408072903816, "dur": 34357, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751408072903806, "dur": 34369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751408072938204, "dur": 2035, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751408072940244, "dur": 1141012, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062421649, "dur": 34234, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062455889, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_22D039E8A3335822.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751408062456000, "dur": 120, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_22D039E8A3335822.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751408062456123, "dur": 340, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_BD8ABEB80D593DCC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751408062456474, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphViewModule.dll_BD8ABEB80D593DCC.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751408062456607, "dur": 124, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_611E434C56DD2756.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751408062457340, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751408062457450, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751408062457702, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751408062457787, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062458007, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751408062458269, "dur": 679, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751408062459110, "dur": 72, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751408062459184, "dur": 210, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062459598, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751408062459667, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751408062459816, "dur": 103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751408062460063, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 4, "ts": 1751408062460439, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4691676790321305727.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751408062460866, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9675442845102135732.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751408062460988, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062461202, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062461414, "dur": 318, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062461732, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062461959, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062462204, "dur": 918, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.universal\\Shaders\\Shaders.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751408062462184, "dur": 1120, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062463305, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062463544, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062463755, "dur": 589, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062464345, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062464582, "dur": 240, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062464822, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062465079, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062465315, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062465531, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062465767, "dur": 345, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062466113, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062466360, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062466627, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062466899, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062467101, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062467307, "dur": 259, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062467648, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062467867, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062468094, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062468302, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062468535, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062468748, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062468972, "dur": 382, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062469355, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751408062469561, "dur": 1516, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751408062471138, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751408062471321, "dur": 1692, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751408062473076, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751408062473282, "dur": 157, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062473450, "dur": 225, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751408062473723, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751408062473782, "dur": 608, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751408062474442, "dur": 449, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751408062474892, "dur": 96, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062475000, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751408062475228, "dur": 720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751408062476261, "dur": 321, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062476582, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062476808, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062477046, "dur": 271, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062477317, "dur": 236, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062477751, "dur": 525, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collections\\Unity.Collections\\NativeList.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751408062477731, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062478804, "dur": 634, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.collab-proxy\\Editor\\Views\\PendingChanges\\Dialogs\\CheckinConflictsDialog.cs"}}, {"pid": 12345, "tid": 4, "ts": 1751408062478385, "dur": 1056, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062479441, "dur": 1057, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062480500, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751408062480660, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062480717, "dur": 1511, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751408062482229, "dur": 72, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062482354, "dur": 156, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751408062482529, "dur": 688, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751408062483288, "dur": 816, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062484106, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751408062484313, "dur": 621, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751408062484935, "dur": 81, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062485071, "dur": 977, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062486050, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751408062486314, "dur": 112939, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062599255, "dur": 1921, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751408062601178, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062601246, "dur": 2628, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751408062603876, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062603927, "dur": 220, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751408062604150, "dur": 2426, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751408062606630, "dur": 3390, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751408062610021, "dur": 161, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062610194, "dur": 2361, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751408062612603, "dur": 2298, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751408062614948, "dur": 2363, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751408062617366, "dur": 2360, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751408062619861, "dur": 197, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Postprocessing.Runtime.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751408062621087, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Rendering.LightTransport.Runtime.pdb"}}, {"pid": 12345, "tid": 4, "ts": 1751408062621169, "dur": 374, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751408062621567, "dur": 11459720, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062421734, "dur": 34162, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062455903, "dur": 79, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1DE53F8CAF447C22.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751408062455984, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062456112, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_2C2D2F6DBE48DE8F.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751408062456571, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062456779, "dur": 99, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.PhysicsModule.dll_5E3C6CF57067B635.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751408062457381, "dur": 110, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751408062457650, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751408062457760, "dur": 275, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062458327, "dur": 71, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751408062458915, "dur": 254, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751408062459176, "dur": 183, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062459409, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751408062459864, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751408062459990, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751408062460078, "dur": 544, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062460661, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062460724, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4772145154433932943.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751408062460928, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751408062461008, "dur": 486, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9111662996337956171.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751408062461495, "dur": 304, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062461800, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062462011, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062462255, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062462505, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062462727, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062462937, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062463157, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062463388, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062463604, "dur": 548, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062464153, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062464377, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062464827, "dur": 1169, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.State\\Transitions\\NesterStateTransitionWidget.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751408062464610, "dur": 1386, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062465996, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062466229, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062466468, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062466692, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062466912, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062467127, "dur": 260, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062467387, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062467661, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062467903, "dur": 241, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062468144, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062468387, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062468610, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062468832, "dur": 289, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062469122, "dur": 192, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751408062469357, "dur": 1430, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062470859, "dur": 159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751408062471045, "dur": 780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062471827, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062471880, "dur": 222, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062472126, "dur": 778, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062472962, "dur": 282, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751408062473264, "dur": 208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751408062473509, "dur": 216, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751408062473796, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062474408, "dur": 234, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062474654, "dur": 820, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062475515, "dur": 69, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062475586, "dur": 635, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062476222, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062476463, "dur": 767, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Core\\Graphs\\IGraphElementData.cs"}}, {"pid": 12345, "tid": 5, "ts": 1751408062476351, "dur": 1050, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062477401, "dur": 515, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062477920, "dur": 247, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062478284, "dur": 89, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062478374, "dur": 57, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062478432, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062478713, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751408062478938, "dur": 445, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062479432, "dur": 1104, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062480537, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751408062480868, "dur": 1808, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062482719, "dur": 54, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062482809, "dur": 231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751408062483065, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751408062483124, "dur": 887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062484084, "dur": 201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751408062484317, "dur": 702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062485089, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751408062485246, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062485680, "dur": 386, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062486067, "dur": 114109, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062600178, "dur": 2362, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062602541, "dur": 261, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062602814, "dur": 2387, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062605202, "dur": 361, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062605570, "dur": 76, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062605649, "dur": 2746, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062608441, "dur": 2712, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062611155, "dur": 236, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062611403, "dur": 2389, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062613835, "dur": 2400, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062616236, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062616302, "dur": 2351, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062618704, "dur": 2289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751408062620994, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408062621176, "dur": 10282675, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751408072903875, "dur": 301, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751408072903853, "dur": 329, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751408072904246, "dur": 1787, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751408072906039, "dur": 1175222, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062421831, "dur": 34078, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062455914, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_1AA5CC13572AAA47.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751408062456447, "dur": 68, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CoreModule.dll_13694A2D45790405.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751408062456573, "dur": 106, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062456816, "dur": 77, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_4E97288DD1B11317.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751408062457092, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751408062457241, "dur": 3844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062461159, "dur": 136, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751408062461317, "dur": 6201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062467609, "dur": 271, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751408062467908, "dur": 999, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062468996, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751408062469189, "dur": 1794, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062471103, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751408062471448, "dur": 960, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062472465, "dur": 740, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062473271, "dur": 182, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751408062473474, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751408062473689, "dur": 1780, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062475529, "dur": 633, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751408062476184, "dur": 493, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062476780, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062477023, "dur": 269, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062477293, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062477544, "dur": 249, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062477888, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062478480, "dur": 78, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062478629, "dur": 250, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751408062478903, "dur": 1456, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062480451, "dur": 204, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751408062480679, "dur": 721, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062481401, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062481493, "dur": 184, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751408062481699, "dur": 911, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062482677, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062482741, "dur": 350, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751408062483128, "dur": 723, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062483852, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062483960, "dur": 2096, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062486057, "dur": 78598, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062564655, "dur": 34604, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062599260, "dur": 3032, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062602294, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062602356, "dur": 2522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062604879, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062604941, "dur": 2337, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062607279, "dur": 188, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062607478, "dur": 2324, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062609863, "dur": 2720, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062612648, "dur": 2702, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062615351, "dur": 610, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062615973, "dur": 3294, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751408062619369, "dur": 326, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062619707, "dur": 75, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062619843, "dur": 147, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751408062619996, "dur": 122, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408062620153, "dur": 505, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Toonshader.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751408062620691, "dur": 339, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Toonshader.Editor.pdb"}}, {"pid": 12345, "tid": 6, "ts": 1751408062621180, "dur": 11456731, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751408074077934, "dur": 390, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751408074077912, "dur": 413, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751408074078336, "dur": 2849, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751408074081189, "dur": 76, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062421927, "dur": 34011, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062456088, "dur": 58, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0E44AF698B21F99B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751408062456457, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.CrashReportingModule.dll_A7F411FD3CF1CCBD.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751408062456597, "dur": 203, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_69C31F9D75BAAB88.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751408062456864, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.TerrainModule.dll_7882E8644625FF23.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751408062457160, "dur": 200, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062457360, "dur": 201, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751408062457667, "dur": 131, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751408062457804, "dur": 172, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062458187, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062458272, "dur": 435, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751408062458874, "dur": 64, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751408062458999, "dur": 107, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751408062459191, "dur": 229, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062459470, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751408062459722, "dur": 114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751408062459897, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751408062460110, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751408062460858, "dur": 67, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2240406767038398906.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751408062460977, "dur": 86, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17242669345997350737.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751408062461064, "dur": 355, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062461419, "dur": 273, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062461693, "dur": 298, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062461991, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062462202, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062462476, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062462711, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062462940, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062463154, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062463378, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062463580, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062463798, "dur": 594, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Nodes\\Artistic\\Adjustment\\ContrastNode.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751408062463784, "dur": 796, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062464580, "dur": 302, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062464883, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062465100, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062465316, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062465538, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062465979, "dur": 1165, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Editor\\VisualScripting.Flow\\Framework\\Nesting\\NesterUnitDescriptor.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751408062465775, "dur": 1388, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062467163, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062467429, "dur": 244, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062467673, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062467895, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062468108, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062468385, "dur": 835, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.render-pipelines.core\\Runtime\\Lighting\\ProbeVolume\\ProbeReferenceVolume.Binding.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751408062468326, "dur": 1081, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062469408, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751408062469610, "dur": 530, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751408062470187, "dur": 624, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751408062470895, "dur": 916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751408062471812, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062471890, "dur": 172, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751408062472089, "dur": 710, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751408062472800, "dur": 333, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062473173, "dur": 255, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751408062473429, "dur": 421, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062473855, "dur": 602, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751408062474497, "dur": 1399, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751408062475947, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751408062476117, "dur": 1015, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751408062477181, "dur": 83, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062477281, "dur": 158, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751408062477463, "dur": 706, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751408062478383, "dur": 346, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062478849, "dur": 96, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062479427, "dur": 1024, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062480453, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751408062480663, "dur": 495, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751408062481213, "dur": 52, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll_5436F99F8BDCED8B.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751408062481289, "dur": 2417, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062483706, "dur": 2355, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062486062, "dur": 113196, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062599259, "dur": 2247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751408062601567, "dur": 2317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751408062603920, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751408062603995, "dur": 2652, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751408062606692, "dur": 2408, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751408062609147, "dur": 2506, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751408062611698, "dur": 2369, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751408062614114, "dur": 2224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751408062616339, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751408062616403, "dur": 2545, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751408062619001, "dur": 2497, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751408062621555, "dur": 11459703, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062421979, "dur": 33965, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062455945, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityTestProtocolModule.dll_1974DA72EEA9484D.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751408062456001, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062456105, "dur": 54, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.EmbreeModule.dll_AD3F2B6EFF130063.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751408062456463, "dur": 108, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.dll_40E8F641E9958680.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751408062456575, "dur": 106, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_D397B83A68A481D1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751408062457182, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751408062457334, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751408062457401, "dur": 3052, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751408062460527, "dur": 234, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UI.ref.dll_08FEAA520A2EFD60.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751408062460773, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17296387151066238333.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751408062460892, "dur": 93, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/12900333509587573630.rsp"}}, {"pid": 12345, "tid": 8, "ts": 1751408062460986, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062461202, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062461416, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062461679, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062461992, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062462224, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062462481, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062462708, "dur": 347, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062463055, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062463267, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062463497, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062463715, "dur": 568, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062464284, "dur": 581, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062464865, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062465083, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062465298, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062465524, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062465798, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062466038, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062466267, "dur": 323, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062466590, "dur": 237, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062466828, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062467050, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062467280, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062467563, "dur": 287, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062467850, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062468085, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062468291, "dur": 238, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062468530, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062468739, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062468962, "dur": 555, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062469519, "dur": 327, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751408062469864, "dur": 61, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751408062469928, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751408062470182, "dur": 861, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751408062471113, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751408062471274, "dur": 850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751408062472196, "dur": 5201, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751408062477461, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751408062477670, "dur": 882, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751408062478553, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062478630, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751408062479150, "dur": 308, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062479470, "dur": 452, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751408062480008, "dur": 446, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062480455, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751408062480659, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751408062481255, "dur": 189, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751408062481467, "dur": 648, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751408062482195, "dur": 323, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062482519, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751408062482730, "dur": 1887, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751408062484710, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751408062484926, "dur": 1009, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751408062486037, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751408062486243, "dur": 549, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751408062486840, "dur": 540, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751408062487954, "dur": 92, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408062489018, "dur": 8132026, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751408070623150, "dur": 14076, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751408070622635, "dur": 14657, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751408070637582, "dur": 69, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408070638512, "dur": 3323174, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751408073963284, "dur": 10754, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751408073963276, "dur": 12224, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751408073976691, "dur": 216, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751408073977579, "dur": 93497, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751408074077879, "dur": 348, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751408074077870, "dur": 358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751408074078247, "dur": 2947, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 0, "ts": 1751408074089404, "dur": 1378, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 6348, "tid": 658, "ts": 1751408074113355, "dur": 2190, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 6348, "tid": 658, "ts": 1751408074115592, "dur": 2309, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 6348, "tid": 658, "ts": 1751408074107133, "dur": 11916, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}