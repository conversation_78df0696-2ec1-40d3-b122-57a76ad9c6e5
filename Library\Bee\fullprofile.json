{"Instructions Readme": "1) Open Chrome or Edge, 2) go to chrome://tracing, 3) click Load, 4) navigate to this file.", "processInfo": {}, "traceEvents": [{"pid": 21892, "tid": -1, "ph": "M", "name": "process_name", "args": {"name": "BeeDriver"}}, {"pid": 21892, "tid": -1, "ph": "M", "name": "process_sort_index", "args": {"sort_index": "-2"}}, {"pid": 21892, "tid": 1285, "ph": "M", "name": "thread_name", "args": {"name": "Thread Pool Worker"}}, {"pid": 21892, "tid": 1285, "ts": 1751444196017648, "dur": 1373, "ph": "X", "name": "ChromeTraceHeader", "args": {}}, {"pid": 21892, "tid": 1285, "ts": 1751444196024677, "dur": 1024, "ph": "X", "name": "Thread Pool Worker", "args": {}}, {"pid": 21892, "tid": 1, "ph": "M", "name": "thread_name", "args": {"name": ""}}, {"pid": 21892, "tid": 1, "ts": 1751444195505934, "dur": 9249, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21892, "tid": 1, "ts": 1751444195515187, "dur": 173440, "ph": "X", "name": "<Add>b__0", "args": {}}, {"pid": 21892, "tid": 1, "ts": 1751444195688641, "dur": 147241, "ph": "X", "name": "WriteJson", "args": {}}, {"pid": 21892, "tid": 1285, "ts": 1751444196025709, "dur": 15, "ph": "X", "name": "", "args": {}}, {"pid": 21892, "tid": 12884901888, "ph": "M", "name": "thread_name", "args": {"name": "ReadEntireBinlogFromIpcAsync"}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195503363, "dur": 8886, "ph": "X", "name": "WaitForConnectionAsync", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195512252, "dur": 491027, "ph": "X", "name": "UpdateFromStreamAsync", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195513461, "dur": 2970, "ph": "X", "name": "ReadAsync 0", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195516438, "dur": 3106, "ph": "X", "name": "ProcessMessages 20496", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195519548, "dur": 355, "ph": "X", "name": "ReadAsync 20496", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195519908, "dur": 17, "ph": "X", "name": "ProcessMessages 20519", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195519926, "dur": 92, "ph": "X", "name": "ReadAsync 20519", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520022, "dur": 1, "ph": "X", "name": "ProcessMessages 626", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520024, "dur": 66, "ph": "X", "name": "ReadAsync 626", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520094, "dur": 2, "ph": "X", "name": "ProcessMessages 1080", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520097, "dur": 63, "ph": "X", "name": "ReadAsync 1080", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520163, "dur": 1, "ph": "X", "name": "ProcessMessages 70", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520165, "dur": 95, "ph": "X", "name": "ReadAsync 70", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520265, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520316, "dur": 1, "ph": "X", "name": "ProcessMessages 854", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520318, "dur": 86, "ph": "X", "name": "ReadAsync 854", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520407, "dur": 1, "ph": "X", "name": "ProcessMessages 415", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520409, "dur": 38, "ph": "X", "name": "ReadAsync 415", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520449, "dur": 1, "ph": "X", "name": "ProcessMessages 572", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520451, "dur": 180, "ph": "X", "name": "ReadAsync 572", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520634, "dur": 1, "ph": "X", "name": "ProcessMessages 1206", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520637, "dur": 163, "ph": "X", "name": "ReadAsync 1206", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520803, "dur": 1, "ph": "X", "name": "ProcessMessages 1079", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520805, "dur": 54, "ph": "X", "name": "ReadAsync 1079", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520862, "dur": 1, "ph": "X", "name": "ProcessMessages 879", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520864, "dur": 117, "ph": "X", "name": "ReadAsync 879", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195520986, "dur": 48, "ph": "X", "name": "ReadAsync 75", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521036, "dur": 2, "ph": "X", "name": "ProcessMessages 1327", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521070, "dur": 49, "ph": "X", "name": "ReadAsync 1327", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521121, "dur": 1, "ph": "X", "name": "ProcessMessages 1092", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521123, "dur": 80, "ph": "X", "name": "ReadAsync 1092", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521208, "dur": 43, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521253, "dur": 2, "ph": "X", "name": "ProcessMessages 857", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521255, "dur": 40, "ph": "X", "name": "ReadAsync 857", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521299, "dur": 41, "ph": "X", "name": "ReadAsync 368", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521342, "dur": 1, "ph": "X", "name": "ProcessMessages 471", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521343, "dur": 43, "ph": "X", "name": "ReadAsync 471", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521390, "dur": 85, "ph": "X", "name": "ReadAsync 404", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521477, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521479, "dur": 41, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521522, "dur": 1, "ph": "X", "name": "ProcessMessages 435", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521524, "dur": 120, "ph": "X", "name": "ReadAsync 435", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521647, "dur": 29, "ph": "X", "name": "ProcessMessages 437", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521677, "dur": 199, "ph": "X", "name": "ReadAsync 437", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521878, "dur": 2, "ph": "X", "name": "ProcessMessages 2312", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521881, "dur": 94, "ph": "X", "name": "ReadAsync 2312", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521991, "dur": 1, "ph": "X", "name": "ProcessMessages 889", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195521993, "dur": 118, "ph": "X", "name": "ReadAsync 889", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522113, "dur": 1, "ph": "X", "name": "ProcessMessages 1580", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522116, "dur": 41, "ph": "X", "name": "ReadAsync 1580", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522160, "dur": 87, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522251, "dur": 43, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522362, "dur": 2, "ph": "X", "name": "ProcessMessages 1133", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522366, "dur": 32, "ph": "X", "name": "ReadAsync 1133", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522433, "dur": 35, "ph": "X", "name": "ProcessMessages 154", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522470, "dur": 50, "ph": "X", "name": "ReadAsync 154", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522522, "dur": 1, "ph": "X", "name": "ProcessMessages 1621", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522525, "dur": 44, "ph": "X", "name": "ReadAsync 1621", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522572, "dur": 59, "ph": "X", "name": "ReadAsync 1", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522632, "dur": 1, "ph": "X", "name": "ProcessMessages 291", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522634, "dur": 63, "ph": "X", "name": "ReadAsync 291", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522699, "dur": 1, "ph": "X", "name": "ProcessMessages 716", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522701, "dur": 108, "ph": "X", "name": "ReadAsync 716", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522842, "dur": 1, "ph": "X", "name": "ProcessMessages 1208", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522844, "dur": 29, "ph": "X", "name": "ReadAsync 1208", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522877, "dur": 63, "ph": "X", "name": "ReadAsync 269", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522967, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195522969, "dur": 125, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195523096, "dur": 1, "ph": "X", "name": "ProcessMessages 1641", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195523098, "dur": 33, "ph": "X", "name": "ReadAsync 1641", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195523134, "dur": 35, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195523171, "dur": 1, "ph": "X", "name": "ProcessMessages 370", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195523173, "dur": 35, "ph": "X", "name": "ReadAsync 370", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195523211, "dur": 34, "ph": "X", "name": "ReadAsync 455", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195523248, "dur": 33, "ph": "X", "name": "ReadAsync 275", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195523284, "dur": 32, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195523320, "dur": 27, "ph": "X", "name": "ReadAsync 276", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195523350, "dur": 79, "ph": "X", "name": "ReadAsync 81", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195523463, "dur": 1, "ph": "X", "name": "ProcessMessages 785", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195523465, "dur": 37, "ph": "X", "name": "ReadAsync 785", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195523505, "dur": 494, "ph": "X", "name": "ReadAsync 181", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524001, "dur": 1, "ph": "X", "name": "ProcessMessages 1044", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524014, "dur": 98, "ph": "X", "name": "ReadAsync 1044", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524113, "dur": 4, "ph": "X", "name": "ProcessMessages 5271", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524118, "dur": 76, "ph": "X", "name": "ReadAsync 5271", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524198, "dur": 1, "ph": "X", "name": "ProcessMessages 392", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524200, "dur": 36, "ph": "X", "name": "ReadAsync 392", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524238, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524240, "dur": 36, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524279, "dur": 84, "ph": "X", "name": "ReadAsync 423", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524366, "dur": 41, "ph": "X", "name": "ReadAsync 531", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524431, "dur": 1, "ph": "X", "name": "ProcessMessages 1139", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524433, "dur": 165, "ph": "X", "name": "ReadAsync 1139", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524601, "dur": 103, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524715, "dur": 28, "ph": "X", "name": "ProcessMessages 1157", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524745, "dur": 41, "ph": "X", "name": "ReadAsync 1157", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524789, "dur": 1, "ph": "X", "name": "ProcessMessages 1084", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524791, "dur": 35, "ph": "X", "name": "ReadAsync 1084", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524830, "dur": 34, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195524907, "dur": 130, "ph": "X", "name": "ReadAsync 394", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525039, "dur": 1, "ph": "X", "name": "ProcessMessages 966", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525041, "dur": 41, "ph": "X", "name": "ReadAsync 966", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525083, "dur": 2, "ph": "X", "name": "ProcessMessages 1519", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525086, "dur": 38, "ph": "X", "name": "ReadAsync 1519", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525146, "dur": 1, "ph": "X", "name": "ProcessMessages 297", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525148, "dur": 121, "ph": "X", "name": "ReadAsync 297", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525271, "dur": 1, "ph": "X", "name": "ProcessMessages 738", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525273, "dur": 75, "ph": "X", "name": "ReadAsync 738", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525351, "dur": 1, "ph": "X", "name": "ProcessMessages 1192", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525353, "dur": 118, "ph": "X", "name": "ReadAsync 1192", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525474, "dur": 2, "ph": "X", "name": "ProcessMessages 1746", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525477, "dur": 39, "ph": "X", "name": "ReadAsync 1746", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525519, "dur": 1, "ph": "X", "name": "ProcessMessages 544", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525520, "dur": 35, "ph": "X", "name": "ReadAsync 544", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525595, "dur": 1, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525597, "dur": 44, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525644, "dur": 1, "ph": "X", "name": "ProcessMessages 1034", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525646, "dur": 128, "ph": "X", "name": "ReadAsync 1034", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525776, "dur": 1, "ph": "X", "name": "ProcessMessages 512", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525778, "dur": 51, "ph": "X", "name": "ReadAsync 512", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525831, "dur": 2, "ph": "X", "name": "ProcessMessages 1878", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525834, "dur": 70, "ph": "X", "name": "ReadAsync 1878", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525908, "dur": 73, "ph": "X", "name": "ReadAsync 627", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525983, "dur": 1, "ph": "X", "name": "ProcessMessages 862", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195525986, "dur": 42, "ph": "X", "name": "ReadAsync 862", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526061, "dur": 1, "ph": "X", "name": "ProcessMessages 780", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526063, "dur": 73, "ph": "X", "name": "ReadAsync 780", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526138, "dur": 1, "ph": "X", "name": "ProcessMessages 1823", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526140, "dur": 34, "ph": "X", "name": "ReadAsync 1823", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526177, "dur": 1, "ph": "X", "name": "ProcessMessages 388", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526178, "dur": 81, "ph": "X", "name": "ReadAsync 388", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526261, "dur": 1, "ph": "X", "name": "ProcessMessages 1224", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526263, "dur": 40, "ph": "X", "name": "ReadAsync 1224", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526337, "dur": 1, "ph": "X", "name": "ProcessMessages 523", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526339, "dur": 42, "ph": "X", "name": "ReadAsync 523", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526383, "dur": 1, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526385, "dur": 39, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526427, "dur": 1, "ph": "X", "name": "ProcessMessages 598", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526429, "dur": 86, "ph": "X", "name": "ReadAsync 598", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526517, "dur": 1, "ph": "X", "name": "ProcessMessages 561", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526519, "dur": 67, "ph": "X", "name": "ReadAsync 561", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526630, "dur": 1, "ph": "X", "name": "ProcessMessages 882", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526633, "dur": 75, "ph": "X", "name": "ReadAsync 882", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526787, "dur": 2, "ph": "X", "name": "ProcessMessages 1507", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526839, "dur": 109, "ph": "X", "name": "ReadAsync 1507", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526950, "dur": 3, "ph": "X", "name": "ProcessMessages 3158", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195526954, "dur": 33, "ph": "X", "name": "ReadAsync 3158", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527029, "dur": 1, "ph": "X", "name": "ProcessMessages 419", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527031, "dur": 43, "ph": "X", "name": "ReadAsync 419", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527114, "dur": 1, "ph": "X", "name": "ProcessMessages 1075", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527117, "dur": 62, "ph": "X", "name": "ReadAsync 1075", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527180, "dur": 1, "ph": "X", "name": "ProcessMessages 696", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527182, "dur": 41, "ph": "X", "name": "ReadAsync 696", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527290, "dur": 1, "ph": "X", "name": "ProcessMessages 819", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527316, "dur": 73, "ph": "X", "name": "ReadAsync 819", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527390, "dur": 1, "ph": "X", "name": "ProcessMessages 1423", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527393, "dur": 40, "ph": "X", "name": "ReadAsync 1423", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527435, "dur": 1, "ph": "X", "name": "ProcessMessages 876", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527437, "dur": 41, "ph": "X", "name": "ReadAsync 876", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527481, "dur": 1, "ph": "X", "name": "ProcessMessages 410", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527483, "dur": 48, "ph": "X", "name": "ReadAsync 410", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527535, "dur": 1, "ph": "X", "name": "ProcessMessages 187", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527537, "dur": 96, "ph": "X", "name": "ReadAsync 187", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527636, "dur": 2, "ph": "X", "name": "ProcessMessages 1553", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527639, "dur": 45, "ph": "X", "name": "ReadAsync 1553", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527687, "dur": 1, "ph": "X", "name": "ProcessMessages 667", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527690, "dur": 36, "ph": "X", "name": "ReadAsync 667", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527728, "dur": 1, "ph": "X", "name": "ProcessMessages 61", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527730, "dur": 44, "ph": "X", "name": "ReadAsync 61", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527776, "dur": 1, "ph": "X", "name": "ProcessMessages 500", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527778, "dur": 106, "ph": "X", "name": "ReadAsync 500", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527888, "dur": 2, "ph": "X", "name": "ProcessMessages 1235", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195527928, "dur": 75, "ph": "X", "name": "ReadAsync 1235", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528100, "dur": 1, "ph": "X", "name": "ProcessMessages 929", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528103, "dur": 76, "ph": "X", "name": "ReadAsync 929", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528183, "dur": 3, "ph": "X", "name": "ProcessMessages 2171", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528187, "dur": 48, "ph": "X", "name": "ReadAsync 2171", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528239, "dur": 1, "ph": "X", "name": "ProcessMessages 486", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528241, "dur": 49, "ph": "X", "name": "ReadAsync 486", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528294, "dur": 1, "ph": "X", "name": "ProcessMessages 481", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528297, "dur": 126, "ph": "X", "name": "ReadAsync 481", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528425, "dur": 1, "ph": "X", "name": "ProcessMessages 823", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528427, "dur": 165, "ph": "X", "name": "ReadAsync 823", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528594, "dur": 2, "ph": "X", "name": "ProcessMessages 1807", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528597, "dur": 92, "ph": "X", "name": "ReadAsync 1807", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528691, "dur": 1, "ph": "X", "name": "ProcessMessages 2047", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528694, "dur": 38, "ph": "X", "name": "ReadAsync 2047", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528734, "dur": 1, "ph": "X", "name": "ProcessMessages 666", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528737, "dur": 39, "ph": "X", "name": "ReadAsync 666", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528778, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528780, "dur": 36, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528818, "dur": 1, "ph": "X", "name": "ProcessMessages 349", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528820, "dur": 38, "ph": "X", "name": "ReadAsync 349", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528928, "dur": 1, "ph": "X", "name": "ProcessMessages 489", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195528930, "dur": 39, "ph": "X", "name": "ReadAsync 489", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195529006, "dur": 1, "ph": "X", "name": "ProcessMessages 642", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195529008, "dur": 55, "ph": "X", "name": "ReadAsync 642", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195529066, "dur": 1, "ph": "X", "name": "ProcessMessages 1007", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195529101, "dur": 77, "ph": "X", "name": "ReadAsync 1007", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195529179, "dur": 1, "ph": "X", "name": "ProcessMessages 1229", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195529182, "dur": 72, "ph": "X", "name": "ReadAsync 1229", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195529284, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195529287, "dur": 70, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195529369, "dur": 1, "ph": "X", "name": "ProcessMessages 530", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195530134, "dur": 181, "ph": "X", "name": "ReadAsync 530", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195530317, "dur": 215, "ph": "X", "name": "ProcessMessages 12968", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195530535, "dur": 112, "ph": "X", "name": "ReadAsync 12968", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195530649, "dur": 3, "ph": "X", "name": "ProcessMessages 4661", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195530654, "dur": 42, "ph": "X", "name": "ReadAsync 4661", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195530698, "dur": 1, "ph": "X", "name": "ProcessMessages 618", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195530700, "dur": 33, "ph": "X", "name": "ReadAsync 618", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195530737, "dur": 38, "ph": "X", "name": "ReadAsync 337", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195530779, "dur": 37, "ph": "X", "name": "ReadAsync 554", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195530818, "dur": 1, "ph": "X", "name": "ProcessMessages 519", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195530820, "dur": 37, "ph": "X", "name": "ReadAsync 519", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195530859, "dur": 1, "ph": "X", "name": "ProcessMessages 525", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195530861, "dur": 66, "ph": "X", "name": "ReadAsync 525", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195530930, "dur": 1, "ph": "X", "name": "ProcessMessages 715", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195530932, "dur": 38, "ph": "X", "name": "ReadAsync 715", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195530972, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195530974, "dur": 34, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531010, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531012, "dur": 33, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531048, "dur": 40, "ph": "X", "name": "ReadAsync 244", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531091, "dur": 1, "ph": "X", "name": "ProcessMessages 505", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531093, "dur": 33, "ph": "X", "name": "ReadAsync 505", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531128, "dur": 1, "ph": "X", "name": "ProcessMessages 282", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531130, "dur": 37, "ph": "X", "name": "ReadAsync 282", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531169, "dur": 1, "ph": "X", "name": "ProcessMessages 408", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531171, "dur": 37, "ph": "X", "name": "ReadAsync 408", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531210, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531212, "dur": 36, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531251, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531253, "dur": 37, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531292, "dur": 1, "ph": "X", "name": "ProcessMessages 429", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531294, "dur": 55, "ph": "X", "name": "ReadAsync 429", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531352, "dur": 1, "ph": "X", "name": "ProcessMessages 420", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531353, "dur": 37, "ph": "X", "name": "ReadAsync 420", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531394, "dur": 34, "ph": "X", "name": "ReadAsync 436", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531431, "dur": 1, "ph": "X", "name": "ProcessMessages 383", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531433, "dur": 34, "ph": "X", "name": "ReadAsync 383", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531470, "dur": 37, "ph": "X", "name": "ReadAsync 203", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531510, "dur": 1, "ph": "X", "name": "ProcessMessages 330", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531512, "dur": 38, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531551, "dur": 1, "ph": "X", "name": "ProcessMessages 483", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531578, "dur": 39, "ph": "X", "name": "ReadAsync 483", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531620, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531622, "dur": 37, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531662, "dur": 1, "ph": "X", "name": "ProcessMessages 565", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531663, "dur": 38, "ph": "X", "name": "ReadAsync 565", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531703, "dur": 1, "ph": "X", "name": "ProcessMessages 564", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531705, "dur": 70, "ph": "X", "name": "ReadAsync 564", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531777, "dur": 1, "ph": "X", "name": "ProcessMessages 409", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531779, "dur": 73, "ph": "X", "name": "ReadAsync 409", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531854, "dur": 1, "ph": "X", "name": "ProcessMessages 1055", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531856, "dur": 38, "ph": "X", "name": "ReadAsync 1055", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531896, "dur": 1, "ph": "X", "name": "ProcessMessages 650", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531898, "dur": 37, "ph": "X", "name": "ReadAsync 650", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531938, "dur": 1, "ph": "X", "name": "ProcessMessages 539", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195531975, "dur": 39, "ph": "X", "name": "ReadAsync 539", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532017, "dur": 1, "ph": "X", "name": "ProcessMessages 686", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532019, "dur": 37, "ph": "X", "name": "ReadAsync 686", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532058, "dur": 1, "ph": "X", "name": "ProcessMessages 571", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532060, "dur": 37, "ph": "X", "name": "ReadAsync 571", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532100, "dur": 1, "ph": "X", "name": "ProcessMessages 357", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532102, "dur": 128, "ph": "X", "name": "ReadAsync 357", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532232, "dur": 2, "ph": "X", "name": "ProcessMessages 1245", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532235, "dur": 35, "ph": "X", "name": "ReadAsync 1245", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532272, "dur": 1, "ph": "X", "name": "ProcessMessages 306", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532273, "dur": 32, "ph": "X", "name": "ReadAsync 306", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532308, "dur": 35, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532347, "dur": 63, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532412, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532414, "dur": 41, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532457, "dur": 1, "ph": "X", "name": "ProcessMessages 790", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532458, "dur": 33, "ph": "X", "name": "ReadAsync 790", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532495, "dur": 32, "ph": "X", "name": "ReadAsync 442", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532530, "dur": 32, "ph": "X", "name": "ReadAsync 369", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532564, "dur": 1, "ph": "X", "name": "ProcessMessages 299", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532566, "dur": 31, "ph": "X", "name": "ReadAsync 299", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532599, "dur": 1, "ph": "X", "name": "ProcessMessages 288", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532600, "dur": 28, "ph": "X", "name": "ReadAsync 288", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532656, "dur": 37, "ph": "X", "name": "ReadAsync 265", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532695, "dur": 1, "ph": "X", "name": "ProcessMessages 675", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532697, "dur": 29, "ph": "X", "name": "ReadAsync 675", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532729, "dur": 30, "ph": "X", "name": "ReadAsync 253", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532762, "dur": 32, "ph": "X", "name": "ReadAsync 205", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532796, "dur": 1, "ph": "X", "name": "ProcessMessages 323", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532797, "dur": 53, "ph": "X", "name": "ReadAsync 323", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532852, "dur": 1, "ph": "X", "name": "ProcessMessages 315", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532854, "dur": 33, "ph": "X", "name": "ReadAsync 315", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532890, "dur": 71, "ph": "X", "name": "ReadAsync 333", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532962, "dur": 32, "ph": "X", "name": "ProcessMessages 193", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195532996, "dur": 55, "ph": "X", "name": "ReadAsync 193", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533054, "dur": 30, "ph": "X", "name": "ReadAsync 391", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533086, "dur": 1, "ph": "X", "name": "ProcessMessages 586", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533088, "dur": 34, "ph": "X", "name": "ReadAsync 586", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533125, "dur": 27, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533155, "dur": 85, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533243, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533280, "dur": 1, "ph": "X", "name": "ProcessMessages 414", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533281, "dur": 32, "ph": "X", "name": "ReadAsync 414", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533317, "dur": 39, "ph": "X", "name": "ReadAsync 364", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533358, "dur": 9, "ph": "X", "name": "ProcessMessages 381", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533368, "dur": 78, "ph": "X", "name": "ReadAsync 381", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533449, "dur": 28, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533480, "dur": 29, "ph": "X", "name": "ReadAsync 287", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533520, "dur": 33, "ph": "X", "name": "ReadAsync 227", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533555, "dur": 1, "ph": "X", "name": "ProcessMessages 588", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533556, "dur": 80, "ph": "X", "name": "ReadAsync 588", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533640, "dur": 50, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533692, "dur": 1, "ph": "X", "name": "ProcessMessages 418", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533694, "dur": 39, "ph": "X", "name": "ReadAsync 418", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533736, "dur": 1, "ph": "X", "name": "ProcessMessages 540", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533738, "dur": 30, "ph": "X", "name": "ReadAsync 540", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533772, "dur": 63, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533838, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533887, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533888, "dur": 33, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533924, "dur": 27, "ph": "X", "name": "ReadAsync 399", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195533955, "dur": 76, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195534034, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195534070, "dur": 31, "ph": "X", "name": "ReadAsync 376", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195534114, "dur": 139, "ph": "X", "name": "ReadAsync 330", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195534332, "dur": 1, "ph": "X", "name": "ProcessMessages 302", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195534335, "dur": 77, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195534488, "dur": 49, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195534539, "dur": 64, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195534642, "dur": 46, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195534690, "dur": 72, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195534791, "dur": 1, "ph": "X", "name": "ProcessMessages 1077", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195534794, "dur": 72, "ph": "X", "name": "ReadAsync 1077", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195534868, "dur": 1, "ph": "X", "name": "ProcessMessages 1108", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195534870, "dur": 35, "ph": "X", "name": "ReadAsync 1108", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195534909, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195534946, "dur": 26, "ph": "X", "name": "ReadAsync 459", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195534975, "dur": 27, "ph": "X", "name": "ReadAsync 73", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535005, "dur": 31, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535038, "dur": 1, "ph": "X", "name": "ProcessMessages 503", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535039, "dur": 90, "ph": "X", "name": "ReadAsync 503", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535133, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535170, "dur": 42, "ph": "X", "name": "ReadAsync 510", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535214, "dur": 1, "ph": "X", "name": "ProcessMessages 386", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535216, "dur": 41, "ph": "X", "name": "ReadAsync 386", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535260, "dur": 1, "ph": "X", "name": "ProcessMessages 395", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535262, "dur": 82, "ph": "X", "name": "ReadAsync 395", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535382, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535383, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535466, "dur": 1, "ph": "X", "name": "ProcessMessages 1245", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535468, "dur": 116, "ph": "X", "name": "ReadAsync 1245", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535679, "dur": 1, "ph": "X", "name": "ProcessMessages 580", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535681, "dur": 39, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535722, "dur": 1, "ph": "X", "name": "ProcessMessages 907", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535724, "dur": 34, "ph": "X", "name": "ReadAsync 907", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535761, "dur": 105, "ph": "X", "name": "ReadAsync 352", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535869, "dur": 1, "ph": "X", "name": "ProcessMessages 407", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535871, "dur": 37, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535911, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195535913, "dur": 110, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536027, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536029, "dur": 93, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536126, "dur": 55, "ph": "X", "name": "ProcessMessages 1080", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536184, "dur": 53, "ph": "X", "name": "ReadAsync 1080", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536240, "dur": 2, "ph": "X", "name": "ProcessMessages 1050", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536243, "dur": 72, "ph": "X", "name": "ReadAsync 1050", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536319, "dur": 46, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536368, "dur": 2, "ph": "X", "name": "ProcessMessages 393", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536371, "dur": 43, "ph": "X", "name": "ReadAsync 393", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536416, "dur": 1, "ph": "X", "name": "ProcessMessages 569", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536419, "dur": 116, "ph": "X", "name": "ReadAsync 569", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536539, "dur": 57, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536599, "dur": 1, "ph": "X", "name": "ProcessMessages 797", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536602, "dur": 41, "ph": "X", "name": "ReadAsync 797", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536645, "dur": 1, "ph": "X", "name": "ProcessMessages 260", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536648, "dur": 80, "ph": "X", "name": "ReadAsync 260", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536731, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536733, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536837, "dur": 1, "ph": "X", "name": "ProcessMessages 624", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195536840, "dur": 189, "ph": "X", "name": "ReadAsync 624", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537031, "dur": 2, "ph": "X", "name": "ProcessMessages 1815", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537034, "dur": 34, "ph": "X", "name": "ReadAsync 1815", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537070, "dur": 1, "ph": "X", "name": "ProcessMessages 452", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537072, "dur": 63, "ph": "X", "name": "ReadAsync 452", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537137, "dur": 1, "ph": "X", "name": "ProcessMessages 334", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537138, "dur": 37, "ph": "X", "name": "ReadAsync 334", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537178, "dur": 2, "ph": "X", "name": "ProcessMessages 563", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537181, "dur": 45, "ph": "X", "name": "ReadAsync 563", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537229, "dur": 1, "ph": "X", "name": "ProcessMessages 292", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537230, "dur": 37, "ph": "X", "name": "ReadAsync 292", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537270, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537272, "dur": 155, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537431, "dur": 210, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537644, "dur": 1, "ph": "X", "name": "ProcessMessages 1029", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537646, "dur": 40, "ph": "X", "name": "ReadAsync 1029", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537688, "dur": 1, "ph": "X", "name": "ProcessMessages 844", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537690, "dur": 35, "ph": "X", "name": "ReadAsync 844", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537727, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537729, "dur": 92, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537825, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537907, "dur": 1, "ph": "X", "name": "ProcessMessages 478", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195537909, "dur": 108, "ph": "X", "name": "ReadAsync 478", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538019, "dur": 1, "ph": "X", "name": "ProcessMessages 596", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538021, "dur": 130, "ph": "X", "name": "ReadAsync 596", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538153, "dur": 2, "ph": "X", "name": "ProcessMessages 2140", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538156, "dur": 33, "ph": "X", "name": "ReadAsync 2140", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538191, "dur": 1, "ph": "X", "name": "ProcessMessages 312", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538193, "dur": 30, "ph": "X", "name": "ReadAsync 312", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538226, "dur": 70, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538298, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538301, "dur": 81, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538386, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538433, "dur": 1, "ph": "X", "name": "ProcessMessages 1096", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538436, "dur": 108, "ph": "X", "name": "ReadAsync 1096", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538584, "dur": 41, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538627, "dur": 1, "ph": "X", "name": "ProcessMessages 1117", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538629, "dur": 44, "ph": "X", "name": "ReadAsync 1117", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538678, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538769, "dur": 1, "ph": "X", "name": "ProcessMessages 1260", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538771, "dur": 156, "ph": "X", "name": "ReadAsync 1260", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538975, "dur": 1, "ph": "X", "name": "ProcessMessages 1156", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195538978, "dur": 35, "ph": "X", "name": "ReadAsync 1156", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539016, "dur": 1, "ph": "X", "name": "ProcessMessages 601", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539017, "dur": 37, "ph": "X", "name": "ReadAsync 601", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539058, "dur": 34, "ph": "X", "name": "ReadAsync 493", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539094, "dur": 1, "ph": "X", "name": "ProcessMessages 373", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539095, "dur": 29, "ph": "X", "name": "ReadAsync 373", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539127, "dur": 36, "ph": "X", "name": "ReadAsync 119", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539165, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539167, "dur": 67, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539239, "dur": 119, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539360, "dur": 1, "ph": "X", "name": "ProcessMessages 472", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539363, "dur": 39, "ph": "X", "name": "ReadAsync 472", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539404, "dur": 1, "ph": "X", "name": "ProcessMessages 590", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539405, "dur": 37, "ph": "X", "name": "ReadAsync 590", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539445, "dur": 1, "ph": "X", "name": "ProcessMessages 412", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539446, "dur": 70, "ph": "X", "name": "ReadAsync 412", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539518, "dur": 1, "ph": "X", "name": "ProcessMessages 678", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539520, "dur": 67, "ph": "X", "name": "ReadAsync 678", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539591, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539663, "dur": 1, "ph": "X", "name": "ProcessMessages 487", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539665, "dur": 75, "ph": "X", "name": "ReadAsync 487", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539743, "dur": 1, "ph": "X", "name": "ProcessMessages 608", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539782, "dur": 42, "ph": "X", "name": "ReadAsync 608", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539825, "dur": 1, "ph": "X", "name": "ProcessMessages 781", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539827, "dur": 67, "ph": "X", "name": "ReadAsync 781", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195539934, "dur": 195, "ph": "X", "name": "ReadAsync 202", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540131, "dur": 1, "ph": "X", "name": "ProcessMessages 599", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540133, "dur": 39, "ph": "X", "name": "ReadAsync 599", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540220, "dur": 32, "ph": "X", "name": "ProcessMessages 1348", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540255, "dur": 40, "ph": "X", "name": "ReadAsync 1348", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540297, "dur": 1, "ph": "X", "name": "ProcessMessages 972", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540299, "dur": 38, "ph": "X", "name": "ReadAsync 972", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540378, "dur": 74, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540496, "dur": 1, "ph": "X", "name": "ProcessMessages 1189", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540498, "dur": 65, "ph": "X", "name": "ReadAsync 1189", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540565, "dur": 1, "ph": "X", "name": "ProcessMessages 533", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540567, "dur": 77, "ph": "X", "name": "ReadAsync 533", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540646, "dur": 1, "ph": "X", "name": "ProcessMessages 646", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540648, "dur": 37, "ph": "X", "name": "ReadAsync 646", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540688, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540727, "dur": 33, "ph": "X", "name": "ReadAsync 446", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540764, "dur": 31, "ph": "X", "name": "ReadAsync 407", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540797, "dur": 1, "ph": "X", "name": "ProcessMessages 257", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540799, "dur": 99, "ph": "X", "name": "ReadAsync 257", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195540902, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541013, "dur": 1, "ph": "X", "name": "ProcessMessages 538", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541015, "dur": 34, "ph": "X", "name": "ReadAsync 538", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541050, "dur": 1, "ph": "X", "name": "ProcessMessages 663", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541052, "dur": 61, "ph": "X", "name": "ReadAsync 663", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541116, "dur": 68, "ph": "X", "name": "ReadAsync 216", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541185, "dur": 1, "ph": "X", "name": "ProcessMessages 1033", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541187, "dur": 53, "ph": "X", "name": "ReadAsync 1033", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541272, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541273, "dur": 40, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541381, "dur": 1, "ph": "X", "name": "ProcessMessages 894", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541384, "dur": 64, "ph": "X", "name": "ReadAsync 894", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541450, "dur": 1, "ph": "X", "name": "ProcessMessages 322", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541451, "dur": 38, "ph": "X", "name": "ReadAsync 322", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541492, "dur": 1, "ph": "X", "name": "ProcessMessages 880", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541493, "dur": 31, "ph": "X", "name": "ReadAsync 880", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541526, "dur": 1, "ph": "X", "name": "ProcessMessages 384", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541527, "dur": 99, "ph": "X", "name": "ReadAsync 384", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541631, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541713, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541715, "dur": 41, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541829, "dur": 1, "ph": "X", "name": "ProcessMessages 591", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541832, "dur": 45, "ph": "X", "name": "ReadAsync 591", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541878, "dur": 1, "ph": "X", "name": "ProcessMessages 1074", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541880, "dur": 70, "ph": "X", "name": "ReadAsync 1074", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541952, "dur": 1, "ph": "X", "name": "ProcessMessages 67", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541954, "dur": 39, "ph": "X", "name": "ReadAsync 67", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195541996, "dur": 47, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542045, "dur": 1, "ph": "X", "name": "ProcessMessages 488", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542047, "dur": 38, "ph": "X", "name": "ReadAsync 488", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542161, "dur": 1, "ph": "X", "name": "ProcessMessages 490", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542163, "dur": 30, "ph": "X", "name": "ReadAsync 490", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542217, "dur": 1, "ph": "X", "name": "ProcessMessages 138", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542218, "dur": 159, "ph": "X", "name": "ReadAsync 138", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542380, "dur": 1, "ph": "X", "name": "ProcessMessages 1146", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542382, "dur": 42, "ph": "X", "name": "ReadAsync 1146", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542459, "dur": 1, "ph": "X", "name": "ProcessMessages 964", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542461, "dur": 31, "ph": "X", "name": "ReadAsync 964", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542495, "dur": 50, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542549, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542551, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542600, "dur": 1, "ph": "X", "name": "ProcessMessages 620", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542601, "dur": 107, "ph": "X", "name": "ReadAsync 620", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542744, "dur": 1, "ph": "X", "name": "ProcessMessages 622", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542746, "dur": 77, "ph": "X", "name": "ReadAsync 622", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542825, "dur": 1, "ph": "X", "name": "ProcessMessages 714", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542864, "dur": 33, "ph": "X", "name": "ReadAsync 714", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542900, "dur": 1, "ph": "X", "name": "ProcessMessages 492", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542901, "dur": 32, "ph": "X", "name": "ReadAsync 492", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542937, "dur": 33, "ph": "X", "name": "ReadAsync 272", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195542973, "dur": 34, "ph": "X", "name": "ReadAsync 302", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543010, "dur": 36, "ph": "X", "name": "ReadAsync 498", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543048, "dur": 1, "ph": "X", "name": "ProcessMessages 141", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543051, "dur": 109, "ph": "X", "name": "ReadAsync 141", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543164, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543209, "dur": 1, "ph": "X", "name": "ProcessMessages 1227", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543211, "dur": 72, "ph": "X", "name": "ReadAsync 1227", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543286, "dur": 72, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543361, "dur": 1, "ph": "X", "name": "ProcessMessages 888", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543362, "dur": 30, "ph": "X", "name": "ReadAsync 888", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543395, "dur": 1, "ph": "X", "name": "ProcessMessages 237", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543397, "dur": 81, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543480, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543482, "dur": 42, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543525, "dur": 1, "ph": "X", "name": "ProcessMessages 448", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543527, "dur": 63, "ph": "X", "name": "ReadAsync 448", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543593, "dur": 58, "ph": "X", "name": "ReadAsync 522", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543654, "dur": 35, "ph": "X", "name": "ReadAsync 57", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543693, "dur": 35, "ph": "X", "name": "ReadAsync 262", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543731, "dur": 42, "ph": "X", "name": "ReadAsync 484", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543776, "dur": 1, "ph": "X", "name": "ProcessMessages 358", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543821, "dur": 24, "ph": "X", "name": "ReadAsync 358", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543848, "dur": 1, "ph": "X", "name": "ProcessMessages 21", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543850, "dur": 82, "ph": "X", "name": "ReadAsync 21", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543934, "dur": 1, "ph": "X", "name": "ProcessMessages 603", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195543936, "dur": 33, "ph": "X", "name": "ReadAsync 603", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544011, "dur": 1, "ph": "X", "name": "ProcessMessages 543", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544013, "dur": 28, "ph": "X", "name": "ReadAsync 543", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544044, "dur": 32, "ph": "X", "name": "ReadAsync 58", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544079, "dur": 95, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544176, "dur": 1, "ph": "X", "name": "ProcessMessages 451", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544179, "dur": 45, "ph": "X", "name": "ReadAsync 451", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544268, "dur": 1, "ph": "X", "name": "ProcessMessages 660", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544271, "dur": 45, "ph": "X", "name": "ReadAsync 660", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544319, "dur": 1, "ph": "X", "name": "ProcessMessages 440", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544321, "dur": 44, "ph": "X", "name": "ReadAsync 440", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544367, "dur": 1, "ph": "X", "name": "ProcessMessages 462", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544369, "dur": 45, "ph": "X", "name": "ReadAsync 462", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544417, "dur": 1, "ph": "X", "name": "ProcessMessages 550", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544419, "dur": 44, "ph": "X", "name": "ReadAsync 550", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544466, "dur": 1, "ph": "X", "name": "ProcessMessages 441", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544469, "dur": 91, "ph": "X", "name": "ReadAsync 441", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544562, "dur": 1, "ph": "X", "name": "ProcessMessages 527", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544565, "dur": 39, "ph": "X", "name": "ReadAsync 527", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544606, "dur": 1, "ph": "X", "name": "ProcessMessages 245", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544608, "dur": 43, "ph": "X", "name": "ReadAsync 245", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544698, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544700, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544759, "dur": 2, "ph": "X", "name": "ProcessMessages 1280", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544762, "dur": 41, "ph": "X", "name": "ReadAsync 1280", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544806, "dur": 1, "ph": "X", "name": "ProcessMessages 300", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544808, "dur": 45, "ph": "X", "name": "ReadAsync 300", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544856, "dur": 1, "ph": "X", "name": "ProcessMessages 458", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544859, "dur": 43, "ph": "X", "name": "ReadAsync 458", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544904, "dur": 1, "ph": "X", "name": "ProcessMessages 313", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544906, "dur": 44, "ph": "X", "name": "ReadAsync 313", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544954, "dur": 1, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195544957, "dur": 94, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195545054, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195545057, "dur": 48, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195545107, "dur": 1, "ph": "X", "name": "ProcessMessages 479", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195545109, "dur": 40, "ph": "X", "name": "ReadAsync 479", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195545152, "dur": 90, "ph": "X", "name": "ReadAsync 580", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195545245, "dur": 2, "ph": "X", "name": "ProcessMessages 1014", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195545248, "dur": 36, "ph": "X", "name": "ReadAsync 1014", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195545287, "dur": 1, "ph": "X", "name": "ProcessMessages 439", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195545288, "dur": 31, "ph": "X", "name": "ReadAsync 439", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195545323, "dur": 94, "ph": "X", "name": "ReadAsync 237", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195545421, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195545455, "dur": 1, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195545457, "dur": 95, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195545557, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195545591, "dur": 430, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546025, "dur": 145, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546173, "dur": 10, "ph": "X", "name": "ProcessMessages 1152", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546185, "dur": 39, "ph": "X", "name": "ReadAsync 1152", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546227, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546230, "dur": 33, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546267, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546270, "dur": 96, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546369, "dur": 2, "ph": "X", "name": "ProcessMessages 204", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546372, "dur": 38, "ph": "X", "name": "ReadAsync 204", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546413, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546416, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546455, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546457, "dur": 35, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546497, "dur": 37, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546537, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546540, "dur": 99, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546642, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546644, "dur": 37, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546741, "dur": 3, "ph": "X", "name": "ProcessMessages 208", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546745, "dur": 45, "ph": "X", "name": "ReadAsync 208", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546794, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546797, "dur": 33, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546834, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546836, "dur": 31, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546870, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546872, "dur": 79, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546955, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546958, "dur": 33, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546994, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195546996, "dur": 29, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547030, "dur": 30, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547064, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547068, "dur": 30, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547101, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547103, "dur": 38, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547144, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547146, "dur": 29, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547177, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547180, "dur": 32, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547215, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547218, "dur": 38, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547260, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547262, "dur": 36, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547301, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547304, "dur": 89, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547395, "dur": 2, "ph": "X", "name": "ProcessMessages 140", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547399, "dur": 33, "ph": "X", "name": "ReadAsync 140", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547437, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547440, "dur": 31, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547474, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547476, "dur": 34, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547513, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547515, "dur": 37, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547556, "dur": 1, "ph": "X", "name": "ProcessMessages 60", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547558, "dur": 112, "ph": "X", "name": "ReadAsync 60", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547674, "dur": 3, "ph": "X", "name": "ProcessMessages 324", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547678, "dur": 41, "ph": "X", "name": "ReadAsync 324", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547723, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547725, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547762, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547764, "dur": 34, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547801, "dur": 1, "ph": "X", "name": "ProcessMessages 76", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547804, "dur": 32, "ph": "X", "name": "ReadAsync 76", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547838, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547841, "dur": 89, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547933, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547936, "dur": 38, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547978, "dur": 2, "ph": "X", "name": "ProcessMessages 224", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195547982, "dur": 30, "ph": "X", "name": "ReadAsync 224", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548015, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548059, "dur": 30, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548091, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548094, "dur": 36, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548136, "dur": 33, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548172, "dur": 1, "ph": "X", "name": "ProcessMessages 100", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548175, "dur": 32, "ph": "X", "name": "ReadAsync 100", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548211, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548213, "dur": 109, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548326, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548329, "dur": 46, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548439, "dur": 3, "ph": "X", "name": "ProcessMessages 240", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548443, "dur": 43, "ph": "X", "name": "ReadAsync 240", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548489, "dur": 2, "ph": "X", "name": "ProcessMessages 236", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548493, "dur": 35, "ph": "X", "name": "ReadAsync 236", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548532, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548536, "dur": 101, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548640, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548643, "dur": 143, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548848, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548851, "dur": 39, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548894, "dur": 2, "ph": "X", "name": "ProcessMessages 196", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548898, "dur": 36, "ph": "X", "name": "ReadAsync 196", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548937, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548940, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548974, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195548977, "dur": 41, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549021, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549023, "dur": 32, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549058, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549060, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549095, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549097, "dur": 34, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549134, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549137, "dur": 33, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549172, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549175, "dur": 88, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549266, "dur": 2, "ph": "X", "name": "ProcessMessages 176", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549269, "dur": 33, "ph": "X", "name": "ReadAsync 176", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549305, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549307, "dur": 30, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549340, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549342, "dur": 32, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549378, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549380, "dur": 19, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549403, "dur": 25, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549431, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549433, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549531, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549534, "dur": 31, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549568, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549570, "dur": 31, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549604, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549607, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549642, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549644, "dur": 91, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549738, "dur": 1, "ph": "X", "name": "ProcessMessages 124", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549741, "dur": 30, "ph": "X", "name": "ReadAsync 124", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549774, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549776, "dur": 31, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549810, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549812, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549845, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549847, "dur": 30, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549879, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549881, "dur": 29, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549972, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195549975, "dur": 35, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195550012, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195550016, "dur": 32, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195550050, "dur": 1, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195550053, "dur": 84, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195550140, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195550143, "dur": 33, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195550238, "dur": 2, "ph": "X", "name": "ProcessMessages 160", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195550241, "dur": 131, "ph": "X", "name": "ReadAsync 160", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195550375, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195550378, "dur": 948, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195551379, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195551381, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195551419, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195551422, "dur": 219, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195551704, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195551706, "dur": 131, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195551840, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195551842, "dur": 5621, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195557492, "dur": 46, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195557540, "dur": 138, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195557764, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195557768, "dur": 217, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195558034, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195558123, "dur": 614, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195558798, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195558800, "dur": 76, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195558879, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195558881, "dur": 95, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195558981, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195559019, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195559022, "dur": 55, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195559083, "dur": 22, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195559135, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195559138, "dur": 78, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195559306, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195559308, "dur": 70, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195559381, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195559383, "dur": 73, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195559653, "dur": 45, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195559701, "dur": 192, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195559945, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195559948, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195560103, "dur": 48, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195560155, "dur": 126, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195560284, "dur": 97, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195560385, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195560469, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195560580, "dur": 2, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195560584, "dur": 57, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195560644, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195560647, "dur": 31, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195560680, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195560682, "dur": 280, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195560967, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195560970, "dur": 136, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195561158, "dur": 54, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195561215, "dur": 35, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195561304, "dur": 59, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195561366, "dur": 148, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195561517, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195561520, "dur": 84, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195561606, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195561609, "dur": 221, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195562054, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195562059, "dur": 276, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195562338, "dur": 1, "ph": "X", "name": "ProcessMessages 112", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195562341, "dur": 132, "ph": "X", "name": "ReadAsync 112", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195562477, "dur": 2, "ph": "X", "name": "ProcessMessages 80", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195562480, "dur": 129, "ph": "X", "name": "ReadAsync 80", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195562612, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195562615, "dur": 132, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195562877, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195562881, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195562966, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195562969, "dur": 245, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195563264, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195563266, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195563397, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195563401, "dur": 140, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195563629, "dur": 45, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195563677, "dur": 112, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195563847, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195563891, "dur": 86, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195564023, "dur": 82, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195564108, "dur": 79, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195564190, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195564193, "dur": 115, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195564370, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195564373, "dur": 141, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195564574, "dur": 1, "ph": "X", "name": "ProcessMessages 28", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195564576, "dur": 72, "ph": "X", "name": "ReadAsync 28", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195564651, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195564654, "dur": 128, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195564874, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195564922, "dur": 35, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195565032, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195565035, "dur": 111, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195565202, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195565204, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195565241, "dur": 1, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195565304, "dur": 125, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195565480, "dur": 2, "ph": "X", "name": "ProcessMessages 144", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195565485, "dur": 229, "ph": "X", "name": "ReadAsync 144", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195565828, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195565832, "dur": 81, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195565916, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195565920, "dur": 189, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195566316, "dur": 35, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195566354, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195566561, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195566564, "dur": 269, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195566879, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195566881, "dur": 79, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195566964, "dur": 48, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195567014, "dur": 42, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195567157, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195567160, "dur": 110, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195567356, "dur": 818, "ph": "X", "name": "ProcessMessages 40", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195568215, "dur": 92, "ph": "X", "name": "ReadAsync 40", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195568346, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195568350, "dur": 96, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195568538, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195568541, "dur": 198, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195568816, "dur": 1, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195568820, "dur": 65, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195568888, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195568891, "dur": 337, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195569292, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195569295, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195569396, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195569451, "dur": 40, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195569594, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195569599, "dur": 40, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195569687, "dur": 9, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195569699, "dur": 79, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195569781, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195569783, "dur": 308, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195570209, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195570211, "dur": 118, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195570526, "dur": 2, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195570530, "dur": 41, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195570654, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195570658, "dur": 82, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195570772, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195570775, "dur": 571, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195571504, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195571507, "dur": 161, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195571725, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195571729, "dur": 102, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195571902, "dur": 2, "ph": "X", "name": "ProcessMessages 68", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195571907, "dur": 132, "ph": "X", "name": "ReadAsync 68", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195572042, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195572045, "dur": 69, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195572153, "dur": 2, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195572157, "dur": 262, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195572422, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195572516, "dur": 131, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195572708, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195572711, "dur": 82, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195572796, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195572859, "dur": 147, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195573044, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195573047, "dur": 139, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195573269, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195573272, "dur": 117, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195573392, "dur": 2, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195573395, "dur": 166, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195573610, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195573612, "dur": 251, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195573867, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195573870, "dur": 94, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195574067, "dur": 46, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195574117, "dur": 28, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195574207, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195574211, "dur": 43, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195574265, "dur": 2, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195574308, "dur": 161, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195574476, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195574478, "dur": 199, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195574964, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195574998, "dur": 96, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195575097, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195575099, "dur": 248, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195575442, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195575445, "dur": 210, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195575700, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195575703, "dur": 164, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195575976, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195575979, "dur": 170, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195576177, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195576179, "dur": 618, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195576877, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195576880, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195576991, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195576996, "dur": 64244, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195641295, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195641300, "dur": 60, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195641522, "dur": 3390, "ph": "X", "name": "ProcessMessages 189", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195644952, "dur": 2425, "ph": "X", "name": "ReadAsync 189", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195647393, "dur": 118, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195647516, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195647654, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195647658, "dur": 320, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195647982, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195647985, "dur": 56, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195648132, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195648135, "dur": 361, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195648628, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195648632, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195648723, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195648726, "dur": 884, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195649657, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195649660, "dur": 89, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195649753, "dur": 112, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195649869, "dur": 1013, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195650886, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195650889, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195650929, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195650931, "dur": 66, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195651052, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195651055, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195651183, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195651233, "dur": 33, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195651468, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195651471, "dur": 418, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195651893, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195651895, "dur": 125, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195652061, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195652064, "dur": 488, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195652555, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195652557, "dur": 34, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195652594, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195652596, "dur": 564, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195653215, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195653219, "dur": 140, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195653363, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195653365, "dur": 1026, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195654580, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195654585, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195654667, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195654670, "dur": 46, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195654721, "dur": 179, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195654903, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195654906, "dur": 1107, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195656057, "dur": 57, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195656118, "dur": 134, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195656334, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195656338, "dur": 454, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195656856, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195656858, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195656941, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195656943, "dur": 132, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195657081, "dur": 80, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195657164, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195657167, "dur": 172, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195657344, "dur": 78, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195657514, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195657517, "dur": 1012, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195658580, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195658583, "dur": 136, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195658809, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195658812, "dur": 147, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195659006, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195659009, "dur": 132, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195659143, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195659146, "dur": 87, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195659282, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195659285, "dur": 1367, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195660657, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195660660, "dur": 115, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195660779, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195660782, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195660818, "dur": 673, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195661495, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195661497, "dur": 90, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195661641, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195661644, "dur": 1790, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195663440, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195663444, "dur": 44, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195663493, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195663496, "dur": 354, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195663853, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195663856, "dur": 84, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195663943, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195663945, "dur": 107, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195664057, "dur": 27, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195664088, "dur": 303, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195664395, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195664397, "dur": 31, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195664431, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195664433, "dur": 1010, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195665447, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195665449, "dur": 91, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195665543, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195665546, "dur": 554, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195666104, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195666106, "dur": 36, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195666145, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195666147, "dur": 31, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195666181, "dur": 1, "ph": "X", "name": "ProcessMessages 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195666184, "dur": 32, "ph": "X", "name": "ReadAsync 32", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195666219, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195666221, "dur": 32, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195666257, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195666259, "dur": 2146, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195668604, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195668609, "dur": 45, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195668657, "dur": 55, "ph": "X", "name": "ProcessMessages 36", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195668715, "dur": 172, "ph": "X", "name": "ReadAsync 36", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195668891, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195668893, "dur": 37, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195668934, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195668937, "dur": 95, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195669080, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195669084, "dur": 164, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195669370, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195669372, "dur": 265, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195669640, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195669643, "dur": 1200, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195670849, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195670852, "dur": 26, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195670953, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195671034, "dur": 416, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195671454, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195671456, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195671498, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195671501, "dur": 182, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195671687, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195671689, "dur": 502, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195672195, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195672255, "dur": 36, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195672347, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195672349, "dur": 460, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195672813, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195672815, "dur": 46, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195672864, "dur": 8, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195672874, "dur": 388, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195673266, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195673269, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195673305, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195673308, "dur": 90, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195673400, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195673504, "dur": 899, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195674407, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195674410, "dur": 124, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195674587, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195674589, "dur": 139, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195674733, "dur": 33, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195674769, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195674771, "dur": 262, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675037, "dur": 127, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675168, "dur": 2, "ph": "X", "name": "ProcessMessages 84", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675172, "dur": 166, "ph": "X", "name": "ReadAsync 84", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675382, "dur": 2, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675385, "dur": 42, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675430, "dur": 2, "ph": "X", "name": "ProcessMessages 192", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675434, "dur": 118, "ph": "X", "name": "ReadAsync 192", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675556, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675558, "dur": 35, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675596, "dur": 1, "ph": "X", "name": "ProcessMessages 52", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675598, "dur": 32, "ph": "X", "name": "ReadAsync 52", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675633, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675635, "dur": 101, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675739, "dur": 1, "ph": "X", "name": "ProcessMessages 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675742, "dur": 40, "ph": "X", "name": "ReadAsync 64", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675784, "dur": 1, "ph": "X", "name": "ProcessMessages 108", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675787, "dur": 124, "ph": "X", "name": "ReadAsync 108", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675914, "dur": 1, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675916, "dur": 30, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195675952, "dur": 88, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195676044, "dur": 113, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195676161, "dur": 1, "ph": "X", "name": "ProcessMessages 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195676164, "dur": 34, "ph": "X", "name": "ReadAsync 96", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195676202, "dur": 1, "ph": "X", "name": "ProcessMessages 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195676204, "dur": 35, "ph": "X", "name": "ReadAsync 16", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195676242, "dur": 1, "ph": "X", "name": "ProcessMessages 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195676245, "dur": 105, "ph": "X", "name": "ReadAsync 48", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195676353, "dur": 2, "ph": "X", "name": "ProcessMessages 157", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195677994, "dur": 144, "ph": "X", "name": "ReadAsync 157", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195678143, "dur": 14, "ph": "X", "name": "ProcessMessages 979", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195678224, "dur": 109660, "ph": "X", "name": "ReadAsync 979", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195787893, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195787898, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195787950, "dur": 30, "ph": "X", "name": "ProcessMessages 319", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195787982, "dur": 34221, "ph": "X", "name": "ReadAsync 319", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195822222, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195822226, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195822278, "dur": 2, "ph": "X", "name": "ProcessMessages 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195822281, "dur": 16542, "ph": "X", "name": "ReadAsync 4", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195838831, "dur": 10, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195838843, "dur": 49, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195838895, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195838899, "dur": 279, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195839182, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195839184, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195839232, "dur": 2, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195839235, "dur": 83166, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195922415, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195922420, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195922495, "dur": 36, "ph": "X", "name": "ProcessMessages 507", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195922533, "dur": 7495, "ph": "X", "name": "ReadAsync 507", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195930044, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195930048, "dur": 43, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195930093, "dur": 1, "ph": "X", "name": "ProcessMessages 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195930095, "dur": 8682, "ph": "X", "name": "ReadAsync 20", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195938800, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195938803, "dur": 67, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195938873, "dur": 22, "ph": "X", "name": "ProcessMessages 501", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195938896, "dur": 7543, "ph": "X", "name": "ReadAsync 501", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195946454, "dur": 3, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195946459, "dur": 55, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195946518, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195946523, "dur": 2635, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195949168, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195949173, "dur": 64, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195949242, "dur": 31, "ph": "X", "name": "ProcessMessages 34", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195949275, "dur": 37640, "ph": "X", "name": "ReadAsync 34", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195986925, "dur": 2, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195986930, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195986987, "dur": 3, "ph": "X", "name": "ProcessMessages 8", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195986992, "dur": 2990, "ph": "X", "name": "ReadAsync 8", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195989992, "dur": 4, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195990000, "dur": 53, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195990057, "dur": 25, "ph": "X", "name": "ProcessMessages 50", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195990084, "dur": 457, "ph": "X", "name": "ReadAsync 50", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195990546, "dur": 1, "ph": "X", "name": "ProcessMessages 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195990549, "dur": 38, "ph": "X", "name": "ReadAsync 12", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195990591, "dur": 541, "ph": "X", "name": "ProcessMessages 13", "args": {}}, {"pid": 21892, "tid": 12884901888, "ts": 1751444195991136, "dur": 11879, "ph": "X", "name": "ReadAsync 13", "args": {}}, {"pid": 21892, "tid": 1285, "ts": 1751444196025726, "dur": 2011, "ph": "X", "name": "ReadEntireBinlogFromIpcAsync", "args": {}}, {"pid": 21892, "tid": 8589934592, "ph": "M", "name": "thread_name", "args": {"name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync"}}, {"pid": 21892, "tid": 8589934592, "ts": 1751444195498638, "dur": 337283, "ph": "X", "name": "await writeBuildProgramInputDataTask", "args": {}}, {"pid": 21892, "tid": 8589934592, "ts": 1751444195835924, "dur": 7, "ph": "X", "name": "WritePipe.WaitConnectionAsync", "args": {}}, {"pid": 21892, "tid": 8589934592, "ts": 1751444195835932, "dur": 1312, "ph": "X", "name": "WriteDagReadyMessage", "args": {}}, {"pid": 21892, "tid": 1285, "ts": 1751444196027747, "dur": 7, "ph": "X", "name": "WaitForBuildProgramInputDataBeingWrittenAndSendDagReadyMessageAsync", "args": {}}, {"pid": 21892, "tid": 4294967296, "ph": "M", "name": "thread_name", "args": {"name": "BuildAsync"}}, {"pid": 21892, "tid": 4294967296, "ts": 1751444195475550, "dur": 529015, "ph": "X", "name": "RunBackend", "args": {}}, {"pid": 21892, "tid": 4294967296, "ts": 1751444195481871, "dur": 9295, "ph": "X", "name": "BackendProgram.Start", "args": {}}, {"pid": 21892, "tid": 4294967296, "ts": 1751444196004887, "dur": 7928, "ph": "X", "name": "await WaitForAndApplyScriptUpdaters", "args": {}}, {"pid": 21892, "tid": 4294967296, "ts": 1751444196010064, "dur": 247, "ph": "X", "name": "await <PERSON><PERSON>tUp<PERSON><PERSON>", "args": {}}, {"pid": 21892, "tid": 4294967296, "ts": 1751444196012909, "dur": 14, "ph": "X", "name": "await taskToReadBuildProgramOutput", "args": {}}, {"pid": 21892, "tid": 1285, "ts": 1751444196027757, "dur": 11, "ph": "X", "name": "BuildAsync", "args": {}}, {"cat": "", "pid": 12345, "tid": 0, "ts": 0, "ph": "M", "name": "process_name", "args": {"name": "bee_backend"}}, {"pid": 12345, "tid": 0, "ts": 1751444195510094, "dur": 2552, "ph": "X", "name": "DriverInitData", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751444195512666, "dur": 1090, "ph": "X", "name": "RemoveStaleOutputs", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751444195513928, "dur": 93, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "PrepareNodes"}}, {"pid": 12345, "tid": 0, "ts": 1751444195514021, "dur": 345, "ph": "X", "name": "BuildQueueInit", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751444195515790, "dur": 686, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751444195518861, "dur": 1811, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.TestRunner.ref.dll_193EC4CE382CBFB3.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751444195521290, "dur": 97, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp2"}}, {"pid": 12345, "tid": 0, "ts": 1751444195521472, "dur": 92, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ConversionSystem.ref.dll_D58562F0DF82BCBA.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751444195521593, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751444195521860, "dur": 84, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751444195522462, "dur": 95, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751444195522678, "dur": 72, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751444195522848, "dur": 87, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.ref.dll_621CDDF9C514DF8F.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751444195523043, "dur": 80, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751444195523256, "dur": 85, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751444195523526, "dur": 77, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751444195523816, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Postprocessing.Runtime.ref.dll_3152B6A9836FBF3B.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751444195524181, "dur": 94, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751444195524798, "dur": 90, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751444195527670, "dur": 58, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751444195528893, "dur": 64, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 0, "ts": 1751444195530959, "dur": 145, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 0, "ts": 1751444195536551, "dur": 91, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15394042617203071315.rsp"}}, {"pid": 12345, "tid": 0, "ts": 1751444195537771, "dur": 50, "ph": "X", "name": "EmitFirstTimeEnqueue", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 0, "ts": 1751444195514391, "dur": 31813, "ph": "X", "name": "EnqueueRequestedNodes", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751444195546220, "dur": 444642, "ph": "X", "name": "WaitForBuildFinished", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751444195990863, "dur": 149, "ph": "X", "name": "JoinBuildThread", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751444195991188, "dur": 78, "ph": "X", "name": "BuildQueueDestroyTail", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751444195991305, "dur": 1927, "ph": "X", "name": "<PERSON><PERSON>", "args": {"detail": "Write AllBuiltNodes"}}, {"pid": 12345, "tid": 1, "ts": 1751444195514777, "dur": 31458, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195546260, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195546845, "dur": 92, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195548193, "dur": 65, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751444195548458, "dur": 253, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/4747638433968585886.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751444195548778, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751444195548949, "dur": 305, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195549264, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195549370, "dur": 189, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195549787, "dur": 958, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.rsp"}}, {"pid": 12345, "tid": 1, "ts": 1751444195550903, "dur": 543, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195551447, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195551668, "dur": 196, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195551865, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195552075, "dur": 242, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195552317, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195552568, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195552791, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195552976, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195553189, "dur": 266, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195553455, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195554194, "dur": 1305, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.shadergraph\\Editor\\Data\\Graphs\\SerializableMesh.cs"}}, {"pid": 12345, "tid": 1, "ts": 1751444195553640, "dur": 1882, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195555523, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195555751, "dur": 657, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195556409, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195556677, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195556889, "dur": 264, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195557154, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195557376, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195557590, "dur": 193, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195557783, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195558005, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195558246, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195558466, "dur": 229, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195558695, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195558902, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195559123, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195559358, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195559567, "dur": 154, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751444195559748, "dur": 2218, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751444195562032, "dur": 261, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751444195562330, "dur": 3040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751444195565438, "dur": 227, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 1, "ts": 1751444195565685, "dur": 658, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 1, "ts": 1751444195566390, "dur": 762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPP-Configuration Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1751444195567192, "dur": 205, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195567888, "dur": 74045, "ph": "X", "name": "ILPP-Configuration", "args": {"detail": "Library/ilpp-configuration.nevergeneratedoutput"}}, {"pid": 12345, "tid": 1, "ts": 1751444195645745, "dur": 2263, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751444195648009, "dur": 56, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195648085, "dur": 2196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751444195650342, "dur": 2258, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751444195652657, "dur": 2759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.ForUI.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751444195655418, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195655488, "dur": 2231, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751444195657765, "dur": 2103, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751444195659910, "dur": 2211, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751444195662158, "dur": 135, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751444195662297, "dur": 2259, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Burst.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751444195664613, "dur": 2205, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751444195666868, "dur": 2308, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751444195669226, "dur": 2313, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Utilities.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751444195671604, "dur": 2276, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751444195673929, "dur": 2346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 1, "ts": 1751444195676432, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195676560, "dur": 192, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.Timeline.Editor.pdb"}}, {"pid": 12345, "tid": 1, "ts": 1751444195676882, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.AI.Navigation.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751444195677012, "dur": 108, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195677121, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Flow.Editor.dll"}}, {"pid": 12345, "tid": 1, "ts": 1751444195677525, "dur": 160489, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195839410, "dur": 237, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/BuildPipeline"}}, {"pid": 12345, "tid": 1, "ts": 1751444195839648, "dur": 1268, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Runner"}}, {"pid": 12345, "tid": 1, "ts": 1751444195840917, "dur": 168, "ph": "X", "name": "CheckGlobSignature", "args": {"detail": "E:/Unity/Editior/6000.0.34f1/Editor/Data/Tools/Compilation/Unity.ILPP.Trigger"}}, {"pid": 12345, "tid": 1, "ts": 1751444195838015, "dur": 3078, "ph": "X", "name": "CheckDagSignatures", "args": {"detail": ""}}, {"pid": 12345, "tid": 1, "ts": 1751444195841093, "dur": 149674, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195514837, "dur": 31413, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195546264, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195546479, "dur": 288, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.GraphicsStateCollectionSerializerModule.dll_2C2D2F6DBE48DE8F.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751444195546850, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.IMGUIModule.dll_3BACF753C5B41AC5.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751444195547511, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751444195547735, "dur": 2991, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195550906, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195551105, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195551338, "dur": 279, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195551617, "dur": 161, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195551779, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195551979, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195552201, "dur": 246, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195552448, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195552671, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195552885, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195553087, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195553300, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195553503, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195553702, "dur": 586, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195554288, "dur": 294, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195554582, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195554794, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195554997, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195555221, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195555439, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195555662, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195555870, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195556097, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195556314, "dur": 243, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195556557, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195556781, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195556998, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195557256, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195557477, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195557706, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195557927, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195558147, "dur": 442, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195558589, "dur": 448, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195559038, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195559236, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195559462, "dur": 464, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195559927, "dur": 164, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751444195560113, "dur": 522, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Utilities.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195560720, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751444195560980, "dur": 220, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751444195561256, "dur": 552, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195561848, "dur": 616, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195562506, "dur": 89, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.AI.Navigation.Editor.ref.dll_7989A1C8D57EF9BF.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751444195562615, "dur": 207, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751444195562845, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751444195563041, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751444195563205, "dur": 66, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195563277, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751444195563483, "dur": 734, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualStudio.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195564260, "dur": 1122, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195565450, "dur": 243, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751444195565715, "dur": 759, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195566474, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195566563, "dur": 316, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195566879, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195567083, "dur": 452, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195567536, "dur": 354, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195567891, "dur": 269, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751444195568189, "dur": 1685, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195569969, "dur": 166, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751444195570159, "dur": 146, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751444195570329, "dur": 482, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195570864, "dur": 1673, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195572621, "dur": 140, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751444195572782, "dur": 857, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195573725, "dur": 152, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751444195573895, "dur": 665, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195574561, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195574677, "dur": 148, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 2, "ts": 1751444195574843, "dur": 317, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195575220, "dur": 907, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195576128, "dur": 72558, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195648688, "dur": 3114, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195651860, "dur": 3208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195655138, "dur": 2311, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Shaders.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195657494, "dur": 2159, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Runtime.Shared.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195659709, "dur": 4843, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195664606, "dur": 2278, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Searcher.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195666934, "dur": 5714, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195672684, "dur": 301, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195672990, "dur": 2729, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEditor.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 2, "ts": 1751444195675721, "dur": 76, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195675927, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195676624, "dur": 542, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.Editor.dll"}}, {"pid": 12345, "tid": 2, "ts": 1751444195677284, "dur": 151, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195677441, "dur": 88, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Universal.2D.Runtime.pdb"}}, {"pid": 12345, "tid": 2, "ts": 1751444195677545, "dur": 163555, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 2, "ts": 1751444195841100, "dur": 149671, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195514874, "dur": 31392, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195546273, "dur": 80, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.Physics2DModule.dll_22D039E8A3335822.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751444195546514, "dur": 390, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEditor.dll_0E44AF698B21F99B.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751444195546910, "dur": 99, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.JSONSerializeModule.dll_611E434C56DD2756.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751444195547793, "dur": 88, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751444195547893, "dur": 107, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195548010, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195548157, "dur": 66, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751444195548460, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751444195548553, "dur": 50, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751444195548604, "dur": 141, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195548746, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751444195548868, "dur": 52, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 3, "ts": 1751444195548990, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751444195549071, "dur": 57, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195549363, "dur": 186, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195549759, "dur": 298, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.rsp"}}, {"pid": 12345, "tid": 3, "ts": 1751444195550934, "dur": 257, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195551191, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195551409, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195551702, "dur": 2503, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ide.visualstudio\\Editor\\Testing\\TestRunnerCallbacks.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751444195551619, "dur": 2708, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195554327, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195554528, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195554761, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195554970, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195555172, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195555380, "dur": 230, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195555610, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195555822, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195556025, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195556321, "dur": 828, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.visualscripting\\Runtime\\VisualScripting.Flow\\Framework\\Events\\GUI\\OnPointerUp.cs"}}, {"pid": 12345, "tid": 3, "ts": 1751444195556241, "dur": 1053, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195557295, "dur": 343, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195557638, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195557843, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195558054, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195558277, "dur": 250, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195558528, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195558768, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195558999, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195559209, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195559445, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195559721, "dur": 240, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751444195559983, "dur": 1295, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195561279, "dur": 85, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195561409, "dur": 219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751444195561679, "dur": 916, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195562595, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195562732, "dur": 183, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751444195562940, "dur": 177, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751444195563118, "dur": 58, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195563182, "dur": 267, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751444195563490, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195564128, "dur": 844, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Shaders.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195565009, "dur": 193, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751444195565230, "dur": 1321, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195566625, "dur": 232, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751444195566901, "dur": 618, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195567588, "dur": 1396, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195568985, "dur": 2245, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195571231, "dur": 186, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751444195571443, "dur": 835, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195572322, "dur": 183, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195572538, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 3, "ts": 1751444195572727, "dur": 599, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.2D.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195573405, "dur": 2723, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195576128, "dur": 70521, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195646651, "dur": 2569, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Common.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195649267, "dur": 2307, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.TextMeshPro.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195651575, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195651660, "dur": 2191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195653890, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.2D.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195653997, "dur": 2737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195656771, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.InputSystem.TestFramework.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195656831, "dur": 2358, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195659273, "dur": 2927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195662235, "dur": 4677, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195666916, "dur": 2401, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Shared.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195669318, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195669377, "dur": 4150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195673583, "dur": 2289, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Editor.ConversionSystem.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195676539, "dur": 134, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/UnityEditor.UI.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751444195676678, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195676730, "dur": 235, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.pdb"}}, {"pid": 12345, "tid": 3, "ts": 1751444195677109, "dur": 343, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.Shared.Editor.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751444195677522, "dur": 109351, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195786902, "dur": 48323, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751444195786876, "dur": 49928, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195839541, "dur": 382, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 3, "ts": 1751444195840676, "dur": 98855, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp.dll (+pdb)"}}, {"pid": 12345, "tid": 3, "ts": 1751444195946824, "dur": 40768, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751444195946809, "dur": 40786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 3, "ts": 1751444195987639, "dur": 3090, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 4, "ts": 1751444195514924, "dur": 31356, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195546285, "dur": 68, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsCommonModule.dll_1DE53F8CAF447C22.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751444195546421, "dur": 71, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityWebRequestTextureModule.dll_DC466A201EF621B1.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751444195546853, "dur": 92, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ImageConversionModule.dll_9923BABDF0F3077F.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751444195547130, "dur": 55, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.SpriteMaskModule.dll_4E97288DD1B11317.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751444195547674, "dur": 53, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751444195547841, "dur": 79, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.CompilationPipeline.Common.dll_7D2E9826DFBFBE48.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751444195547941, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751444195548074, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Runtime.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751444195548202, "dur": 54, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195548275, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195548449, "dur": 58, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751444195549062, "dur": 149, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751444195549244, "dur": 130, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp2"}}, {"pid": 12345, "tid": 4, "ts": 1751444195549382, "dur": 244, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195549631, "dur": 212, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751444195549927, "dur": 158, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195550091, "dur": 74, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751444195550205, "dur": 867, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.PerformanceTesting.Editor.rsp"}}, {"pid": 12345, "tid": 4, "ts": 1751444195551073, "dur": 377, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195551451, "dur": 369, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195551820, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195552036, "dur": 310, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195552346, "dur": 367, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195552714, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195552943, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195553150, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195553357, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195553561, "dur": 598, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195554159, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195554358, "dur": 449, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195554807, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195555006, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195555211, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195555413, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195555634, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195555843, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195556065, "dur": 210, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195556275, "dur": 245, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195556521, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195556747, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195556963, "dur": 255, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195557218, "dur": 313, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195557532, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195557746, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195557961, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195558162, "dur": 474, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195558636, "dur": 322, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195558958, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195559170, "dur": 235, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195559405, "dur": 202, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195559608, "dur": 210, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751444195559853, "dur": 7932, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751444195567859, "dur": 248, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751444195568128, "dur": 1834, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751444195570042, "dur": 181, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751444195570224, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195570290, "dur": 671, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.GPUDriven.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751444195571016, "dur": 59, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195571084, "dur": 191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751444195571299, "dur": 942, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751444195572291, "dur": 203, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751444195572507, "dur": 667, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195573616, "dur": 332, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751444195573949, "dur": 52, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195574006, "dur": 737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll (+2 others)"}}, {"pid": 12345, "tid": 4, "ts": 1751444195574745, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195574848, "dur": 1271, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195576128, "dur": 235, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 4, "ts": 1751444195576394, "dur": 69676, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195646072, "dur": 3219, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751444195649292, "dur": 2635, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195651935, "dur": 14215, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751444195666210, "dur": 3738, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Multiplayer.Center.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751444195670020, "dur": 3986, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751444195674008, "dur": 78, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195674095, "dur": 2873, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 4, "ts": 1751444195676969, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195677514, "dur": 501, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 4, "ts": 1751444195678042, "dur": 312791, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195515019, "dur": 31274, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195546299, "dur": 55, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityAnalyticsModule.dll_1AA5CC13572AAA47.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751444195546750, "dur": 102, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.ClusterRendererModule.dll_2D31BD0D6ECE4F8B.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751444195546853, "dur": 93, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195547575, "dur": 273, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751444195547985, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195548175, "dur": 61, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751444195548250, "dur": 84, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751444195548871, "dur": 111, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.Editor.rsp2"}}, {"pid": 12345, "tid": 5, "ts": 1751444195549089, "dur": 51, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195549349, "dur": 192, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195550033, "dur": 282, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 5, "ts": 1751444195550317, "dur": 67, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5916180605834378387.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751444195550418, "dur": 130, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/17571664448659802584.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751444195550549, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195550616, "dur": 66, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/2400031028012695012.rsp"}}, {"pid": 12345, "tid": 5, "ts": 1751444195550921, "dur": 185, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195551107, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195551312, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195551512, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195551731, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195551942, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195552162, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195552387, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195552610, "dur": 194, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195552805, "dur": 198, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195553004, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195553212, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195553418, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195553622, "dur": 591, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195554214, "dur": 292, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195554507, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195554723, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195554933, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195555145, "dur": 223, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195555368, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195555589, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195555802, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195556015, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195556232, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195556486, "dur": 232, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195556718, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195556933, "dur": 263, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195557196, "dur": 228, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195557424, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195557638, "dur": 211, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195557849, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195558057, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195558290, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195558494, "dur": 233, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195558728, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195558960, "dur": 203, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195559164, "dur": 251, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195559415, "dur": 342, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195559758, "dur": 194, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751444195559978, "dur": 589, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Searcher.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751444195560628, "dur": 174, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751444195560822, "dur": 676, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.ConversionSystem.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751444195561561, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751444195561728, "dur": 169, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751444195561913, "dur": 646, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751444195562560, "dur": 110, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195562705, "dur": 576, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751444195563301, "dur": 512, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751444195563814, "dur": 90, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195563914, "dur": 583, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Rendering.LightTransport.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751444195564542, "dur": 611, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.Updater.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751444195565154, "dur": 74, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195565290, "dur": 218, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Rendering.LightTransport.Editor.ref.dll_F96E2A343514CFAE.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751444195565510, "dur": 300, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751444195565810, "dur": 61, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195565876, "dur": 781, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Collections.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751444195566658, "dur": 442, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195567167, "dur": 132, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195567299, "dur": 267, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195567567, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195567788, "dur": 220, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195568703, "dur": 1281, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195569986, "dur": 206, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Core.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751444195570223, "dur": 226, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751444195570473, "dur": 492, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipeline.Universal.ShaderLibrary.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751444195571048, "dur": 1042, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195572091, "dur": 1303, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195573395, "dur": 475, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195573872, "dur": 247, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 5, "ts": 1751444195574157, "dur": 537, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 5, "ts": 1751444195574695, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195574774, "dur": 1347, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195576122, "dur": 73207, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195649331, "dur": 3811, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.GPUDriven.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751444195653195, "dur": 4709, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.ShaderLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751444195657905, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195657979, "dur": 3364, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Toonshader.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751444195661344, "dur": 55, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195661413, "dur": 3704, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Mathematics.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751444195665166, "dur": 3952, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Core.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751444195669167, "dur": 3208, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751444195672377, "dur": 63, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195672441, "dur": 519, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/PPv2URPConverters.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751444195672964, "dur": 3024, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.FilmInternalUtilities.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 5, "ts": 1751444195675989, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195676328, "dur": 73, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195676552, "dur": 129, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.PerformanceTesting.Editor.pdb"}}, {"pid": 12345, "tid": 5, "ts": 1751444195676687, "dur": 50, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195676738, "dur": 246, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.VisualScripting.SettingsProvider.Editor.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751444195677121, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Unity.RenderPipelines.Core.Editor.Shared.dll"}}, {"pid": 12345, "tid": 5, "ts": 1751444195677518, "dur": 499, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 5, "ts": 1751444195678047, "dur": 312749, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195515071, "dur": 31233, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195546311, "dur": 98, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityConnectModule.dll_CCD605E26FC2B53B.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751444195546838, "dur": 57, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HierarchyCoreModule.dll_782156EAC64F1FC2.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751444195546897, "dur": 93, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputModule.dll_69C31F9D75BAAB88.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751444195547596, "dur": 128, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751444195547756, "dur": 57, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751444195547855, "dur": 75, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Burst.Cecil.Rocks.dll_0B080C2548A382FC.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751444195548250, "dur": 50, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Postprocessing.Runtime.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751444195548577, "dur": 53, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751444195548726, "dur": 85, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751444195548826, "dur": 103, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751444195549248, "dur": 122, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Shared.Editor.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 6, "ts": 1751444195549377, "dur": 317, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195549699, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.SettingsProvider.Editor.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751444195550298, "dur": 59, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/1418726328684876121.rsp"}}, {"pid": 12345, "tid": 6, "ts": 1751444195550932, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195551149, "dur": 281, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195551430, "dur": 288, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195551718, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195551943, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195552158, "dur": 212, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195552370, "dur": 234, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195552604, "dur": 187, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195552791, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195553008, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195553215, "dur": 204, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195553419, "dur": 200, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195553619, "dur": 601, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195554220, "dur": 217, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195554438, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195554654, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195554850, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195555065, "dur": 201, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195555266, "dur": 199, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195555465, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195555680, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195555900, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195556119, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195556335, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195556609, "dur": 222, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195556832, "dur": 239, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195557071, "dur": 258, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195557329, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195557590, "dur": 1721, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.ugui\\Runtime\\TMP\\TMP_FontAssetCommon.cs"}}, {"pid": 12345, "tid": 6, "ts": 1751444195557556, "dur": 2184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195559769, "dur": 346, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751444195560138, "dur": 2900, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751444195563039, "dur": 60, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195563128, "dur": 213, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751444195563362, "dur": 990, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751444195564431, "dur": 1355, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751444195565873, "dur": 87, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195566169, "dur": 82, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195566299, "dur": 272, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195566571, "dur": 275, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195566846, "dur": 400, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195567270, "dur": 656, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195567928, "dur": 343, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll.mvfrm"}}, {"pid": 12345, "tid": 6, "ts": 1751444195568272, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195568427, "dur": 420, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.ForUI.dll (+2 others)"}}, {"pid": 12345, "tid": 6, "ts": 1751444195568921, "dur": 3171, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195572093, "dur": 1518, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195573612, "dur": 2510, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195576123, "dur": 72566, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195648690, "dur": 3014, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Collections.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751444195651750, "dur": 2191, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751444195653978, "dur": 7564, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751444195661547, "dur": 2353, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PlasticSCM.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751444195663905, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195663975, "dur": 2903, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Postprocessing.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751444195666879, "dur": 104, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195667017, "dur": 2716, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.CollabProxy.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751444195669788, "dur": 2769, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751444195672558, "dur": 243, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195672802, "dur": 2692, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.PerformanceTesting.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751444195675498, "dur": 2471, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.AI.Navigation.Updater.dll (+pdb)"}}, {"pid": 12345, "tid": 6, "ts": 1751444195678031, "dur": 252354, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 6, "ts": 1751444195930409, "dur": 323, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751444195930386, "dur": 348, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.dll"}}, {"pid": 12345, "tid": 6, "ts": 1751444195930777, "dur": 60078, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195515112, "dur": 31205, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195546377, "dur": 121, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.UnityCurlModule.dll_BF2CDC1257CA045F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751444195546846, "dur": 74, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.HotReloadModule.dll_0D58F8E6C2E92DF4.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751444195546927, "dur": 139, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.LocalizationModule.dll_9D5FC13BBE3E7A4F.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751444195547554, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751444195547716, "dur": 153, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Runtime.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751444195548073, "dur": 212, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.rsp2"}}, {"pid": 12345, "tid": 7, "ts": 1751444195548314, "dur": 182, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751444195548738, "dur": 73, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Core.Editor.Shared.dll.mvfrm.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751444195549136, "dur": 149, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.UnityAdditionalFile.txt"}}, {"pid": 12345, "tid": 7, "ts": 1751444195549368, "dur": 268, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195549641, "dur": 84, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteText Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Editor.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751444195550141, "dur": 81, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/5546506141355401238.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751444195550362, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195550507, "dur": 56, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15085861467720516389.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751444195550564, "dur": 75, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/9870516708743257357.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751444195550640, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195550779, "dur": 65, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195550853, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "WriteResponseFile Library/Bee/artifacts/rsp/15183149355271759364.rsp"}}, {"pid": 12345, "tid": 7, "ts": 1751444195550976, "dur": 268, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195551245, "dur": 209, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195551454, "dur": 253, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195551707, "dur": 213, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195551920, "dur": 205, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195552163, "dur": 274, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195552437, "dur": 283, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195552720, "dur": 221, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195552942, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195553134, "dur": 195, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195553329, "dur": 192, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195553522, "dur": 184, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195553706, "dur": 819, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195554526, "dur": 218, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195554744, "dur": 226, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195554970, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195555186, "dur": 208, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195555394, "dur": 620, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195556014, "dur": 214, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195556228, "dur": 256, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195556484, "dur": 227, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195556712, "dur": 225, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195556937, "dur": 639, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195557577, "dur": 206, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195557783, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195558000, "dur": 207, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195558232, "dur": 216, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195558449, "dur": 231, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195558680, "dur": 224, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195558904, "dur": 215, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195559775, "dur": 673, "ph": "X", "name": "File", "args": {"detail": "Library\\PackageCache\\com.unity.timeline\\Editor\\treeview\\Snapping\\ISnappable.cs"}}, {"pid": 12345, "tid": 7, "ts": 1751444195559119, "dur": 1329, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195560450, "dur": 198, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751444195560673, "dur": 519, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.AI.Navigation.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195561260, "dur": 750, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Multiplayer.Center.Common.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195562064, "dur": 150, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751444195562256, "dur": 793, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Mathematics.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195563050, "dur": 68, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195563162, "dur": 297, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751444195563480, "dur": 1252, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Timeline.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195564769, "dur": 123, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/Unity.Timeline.Editor.ref.dll_0D3D3C0B73557612.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751444195564936, "dur": 331, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751444195565327, "dur": 703, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.FilmInternalUtilities.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195566196, "dur": 320, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195566559, "dur": 311, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195566870, "dur": 219, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195567261, "dur": 153, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195567414, "dur": 582, "ph": "X", "name": "EarlyStatNonGeneratedFile", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195567998, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751444195568216, "dur": 927, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.InputSystem.TestFramework.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195569189, "dur": 1375, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195570565, "dur": 1795, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195572405, "dur": 106, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.Flow.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195572544, "dur": 179, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751444195572746, "dur": 528, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.VisualScripting.State.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195573334, "dur": 168, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751444195573523, "dur": 464, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195574070, "dur": 171, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751444195574257, "dur": 642, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Toonshader.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195574966, "dur": 1150, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195576120, "dur": 230, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll.mvfrm"}}, {"pid": 12345, "tid": 7, "ts": 1751444195576372, "dur": 70235, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195646610, "dur": 2040, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195648652, "dur": 80, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195648732, "dur": 80, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/UnityEngine.UI.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195648816, "dur": 7965, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Timeline.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195656833, "dur": 2302, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.Universal.Config.Runtime.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195659136, "dur": 71, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195659222, "dur": 2284, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195661508, "dur": 117, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195661626, "dur": 657, "ph": "X", "name": "EmitNodeUpToDate", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195662288, "dur": 2470, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.ShaderGraph.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195664808, "dur": 4792, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.Rendering.LightTransport.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195669658, "dur": 2398, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.Flow.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195672058, "dur": 149, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195672278, "dur": 2786, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.SettingsProvider.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195675065, "dur": 53, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195675132, "dur": 2837, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Unity.VisualScripting.State.Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 7, "ts": 1751444195678032, "dur": 268787, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 7, "ts": 1751444195946844, "dur": 267, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751444195946820, "dur": 293, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751444195947189, "dur": 2573, "ph": "X", "name": "CopyFiles", "args": {"detail": "Library/ScriptAssemblies/Assembly-CSharp.pdb"}}, {"pid": 12345, "tid": 7, "ts": 1751444195949774, "dur": 41061, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751444195515166, "dur": 31219, "ph": "X", "name": "FirstLock", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751444195546886, "dur": 82, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor Library/Bee/artifacts/mvdfrm/UnityEngine.InputLegacyModule.dll_D397B83A68A481D1.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751444195547459, "dur": 121, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751444195547609, "dur": 4405, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEngine.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751444195552146, "dur": 196, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751444195552365, "dur": 5762, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.TestRunner.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751444195558222, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751444195558421, "dur": 983, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/UnityEditor.UI.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751444195559498, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751444195559681, "dur": 1737, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751444195561418, "dur": 77, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751444195561582, "dur": 202, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751444195561824, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.Burst.CodeGen.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751444195562478, "dur": 197, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751444195562701, "dur": 6615, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.PlasticSCM.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751444195569389, "dur": 162, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751444195569574, "dur": 601, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.CollabProxy.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751444195570237, "dur": 188, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751444195570450, "dur": 490, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Config.Runtime.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751444195571037, "dur": 165, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751444195571235, "dur": 773, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.TextMeshPro.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751444195572104, "dur": 1142, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751444195573247, "dur": 349, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751444195573620, "dur": 1675, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.ShaderGraph.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751444195575296, "dur": 62, "ph": "X", "name": "OutputFilesMissingFor", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751444195575380, "dur": 108, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751444195575501, "dur": 550, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Unity.RenderPipelines.Universal.Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751444195576116, "dur": 180, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "MovedFromExtractor-Combine Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll.mvfrm"}}, {"pid": 12345, "tid": 8, "ts": 1751444195576315, "dur": 322, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/PPv2URPConverters.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751444195576672, "dur": 319, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751444195577421, "dur": 82, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751444195578555, "dur": 206221, "ph": "X", "name": "Csc", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751444195787432, "dur": 19196, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.ref.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751444195786865, "dur": 19850, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "Csc Library/Bee/artifacts/1900b0aE.dag/Assembly-CSharp-Editor.dll (+2 others)"}}, {"pid": 12345, "tid": 8, "ts": 1751444195806774, "dur": 28433, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\Assembly-CSharp.dll"}}, {"pid": 12345, "tid": 8, "ts": 1751444195806771, "dur": 29966, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "ILPostProcess Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751444195838905, "dur": 487, "ph": "X", "name": "StoreTimestampsOfNonGeneratedInputFiles", "args": {"detail": ""}}, {"pid": 12345, "tid": 8, "ts": 1751444195840395, "dur": 82758, "ph": "X", "name": "ILPostProcess", "args": {"detail": "Library/Bee/artifacts/1900b0aE.dag/post-processed/Assembly-CSharp-Editor.dll (+pdb)"}}, {"pid": 12345, "tid": 8, "ts": 1751444195930389, "dur": 342, "ph": "X", "name": "ComputeFileSignatureSha1", "args": {"detail": "Library\\Bee\\artifacts\\1900b0aE.dag\\post-processed\\Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751444195930379, "dur": 354, "ph": "X", "name": "CheckInputSignature", "args": {"detail": "CopyFiles Library/ScriptAssemblies/Assembly-CSharp-Editor.pdb"}}, {"pid": 12345, "tid": 8, "ts": 1751444195930772, "dur": 60131, "ph": "X", "name": "WaitingForWork", "cname": "thread_state_sleeping", "args": {"detail": ""}}, {"pid": 12345, "tid": 0, "ts": 1751444196001703, "dur": 1569, "ph": "X", "name": "ProfilerWriteOutput"}, {"pid": 21892, "tid": 1285, "ts": 1751444196028292, "dur": 3626, "ph": "X", "name": "Wait for external events", "args": {"First to finish": "backend1.traceevents"}}, {"pid": 21892, "tid": 1285, "ts": 1751444196032000, "dur": 15591, "ph": "X", "name": "backend1.traceevents", "args": {}}, {"pid": 21892, "tid": 1285, "ts": 1751444196023071, "dur": 26564, "ph": "X", "name": "Write chrome-trace events", "args": {}}, {}]}