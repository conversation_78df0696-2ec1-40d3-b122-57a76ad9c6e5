using UnityEngine;
using System.Collections.Generic;

public class GrassController : MonoBehaviour
{
    [Header("Grass Settings")]
    public Material grassMaterial;
    public Mesh grassMesh;
    public Terrain terrain;
    
    [Header("Generation Parameters")]
    public int grassCount = 10000;
    public float grassDensity = 1.0f;
    public float minScale = 0.8f;
    public float maxScale = 1.2f;
    
    [Header("Randomization")]
    [Range(0f, 1f)]
    public float altTextureChance = 0.3f;
    
    // GPU Instancing data
    private Matrix4x4[] grassMatrices;
    private Vector4[] terrainWorldPositions;
    private Vector4[] terrainNormals;
    private float[] randomSeeds;
    
    // Material property blocks for instancing
    private MaterialPropertyBlock propertyBlock;
    
    // Shader property IDs
    private int terrainWorldPosID;
    private int terrainNormalID;
    private int randomSeedID;
    
    void Start()
    {
        InitializeShaderProperties();
        GenerateGrassInstances();
    }
    
    void InitializeShaderProperties()
    {
        terrainWorldPosID = Shader.PropertyToID("_TerrainWorldPos");
        terrainNormalID = Shader.PropertyToID("_TerrainNormal");
        randomSeedID = Shader.PropertyToID("_RandomSeed");
        
        propertyBlock = new MaterialPropertyBlock();
    }
    
    void GenerateGrassInstances()
    {
        if (terrain == null || grassMaterial == null || grassMesh == null)
        {
            Debug.LogError("Missing required components for grass generation!");
            return;
        }

        // Check if material supports instancing
        if (!grassMaterial.enableInstancing)
        {
            Debug.LogError("Grass material must have 'Enable GPU Instancing' checked in the material inspector!");
            return;
        }
        
        TerrainData terrainData = terrain.terrainData;
        Vector3 terrainPosition = terrain.transform.position;
        Vector3 terrainSize = terrainData.size;
        
        // Initialize arrays
        grassMatrices = new Matrix4x4[grassCount];
        terrainWorldPositions = new Vector4[grassCount];
        terrainNormals = new Vector4[grassCount];
        randomSeeds = new float[grassCount];
        
        for (int i = 0; i < grassCount; i++)
        {
            // Generate random position on terrain
            float x = Random.Range(0f, 1f);
            float z = Random.Range(0f, 1f);
            
            // Get terrain height and normal at this position
            float height = terrainData.GetInterpolatedHeight(x, z);
            Vector3 normal = terrainData.GetInterpolatedNormal(x, z);
            
            // Convert to world space
            Vector3 worldPos = new Vector3(
                terrainPosition.x + x * terrainSize.x,
                terrainPosition.y + height,
                terrainPosition.z + z * terrainSize.z
            );
            
            // Store terrain data for shader
            terrainWorldPositions[i] = new Vector4(worldPos.x, worldPos.y, worldPos.z, 0);
            terrainNormals[i] = new Vector4(normal.x, normal.y, normal.z, 0);
            randomSeeds[i] = Random.Range(0f, 1f);
            
            // Create grass transform matrix
            float scale = Random.Range(minScale, maxScale);
            float rotation = Random.Range(0f, 360f);
            
            Vector3 grassPosition = worldPos + Vector3.up * 0.1f; // Slightly above terrain
            Quaternion grassRotation = Quaternion.Euler(0, rotation, 0);
            Vector3 grassScale = Vector3.one * scale;
            
            grassMatrices[i] = Matrix4x4.TRS(grassPosition, grassRotation, grassScale);
        }
        
        Debug.Log($"Generated {grassCount} grass instances");
    }
    
    void Update()
    {
        if (grassMatrices == null || grassMaterial == null || grassMesh == null)
            return;
            
        RenderGrass();
    }
    
    void RenderGrass()
    {
        // Set up material property block with terrain data
        propertyBlock.SetVectorArray(terrainWorldPosID, terrainWorldPositions);
        propertyBlock.SetVectorArray(terrainNormalID, terrainNormals);
        propertyBlock.SetFloatArray(randomSeedID, randomSeeds);
        
        // Render grass instances in batches (Unity has a limit of 1023 instances per batch)
        int batchSize = 1023;
        int batches = Mathf.CeilToInt((float)grassCount / batchSize);
        
        for (int batch = 0; batch < batches; batch++)
        {
            int startIndex = batch * batchSize;
            int count = Mathf.Min(batchSize, grassCount - startIndex);
            
            // Create batch arrays
            Matrix4x4[] batchMatrices = new Matrix4x4[count];
            Vector4[] batchTerrainPos = new Vector4[count];
            Vector4[] batchTerrainNormals = new Vector4[count];
            float[] batchRandomSeeds = new float[count];
            
            System.Array.Copy(grassMatrices, startIndex, batchMatrices, 0, count);
            System.Array.Copy(terrainWorldPositions, startIndex, batchTerrainPos, 0, count);
            System.Array.Copy(terrainNormals, startIndex, batchTerrainNormals, 0, count);
            System.Array.Copy(randomSeeds, startIndex, batchRandomSeeds, 0, count);
            
            // Set batch-specific properties
            propertyBlock.SetVectorArray(terrainWorldPosID, batchTerrainPos);
            propertyBlock.SetVectorArray(terrainNormalID, batchTerrainNormals);
            propertyBlock.SetFloatArray(randomSeedID, batchRandomSeeds);
            
            // Render batch
            Graphics.DrawMeshInstanced(
                grassMesh,
                0,
                grassMaterial,
                batchMatrices,
                count,
                propertyBlock
            );
        }
    }
    
    void OnValidate()
    {
        // Regenerate grass when parameters change in editor
        if (Application.isPlaying && grassMatrices != null)
        {
            GenerateGrassInstances();
        }
    }
}
