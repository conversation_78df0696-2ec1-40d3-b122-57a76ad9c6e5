using UnityEngine;
using System.Collections.Generic;

public class GrassController : MonoBehaviour
{
    [Header("Grass Settings")]
    public Material grassMaterial;
    public Mesh grassMesh;
    public Terrain terrain;
    
    [Header("Generation Parameters")]
    [Range(0.1f, 10f)]
    public float grassDensity = 2.0f; // Grass instances per unit
    public float minScale = 0.8f;
    public float maxScale = 1.2f;
    public float yOffset = 0.0f; // Additional Y offset from terrain surface

    [Header("Randomization")]
    [Range(0f, 1f)]
    public float altTextureChance = 0.3f;
    [Range(0f, 1f)]
    public float popoutRate = 0.8f; // Percentage of positions that get grass
    [Range(0f, 5f)]
    public float popoutDistance = 1.0f; // Random offset distance from grid positions
    
    // GPU Instancing data
    private Matrix4x4[] grassMatrices;
    private Vector4[] terrainWorldPositions;
    private Vector4[] terrainNormals;
    private float[] randomSeeds;
    private int totalGrassCount;
    
    // Material property blocks for instancing
    private MaterialPropertyBlock propertyBlock;
    
    // Shader property IDs
    private int terrainWorldPosID;
    private int terrainNormalID;
    private int randomSeedID;
    
    void Start()
    {
        InitializeShaderProperties();
        GenerateGrassInstances();
    }
    
    void InitializeShaderProperties()
    {
        terrainWorldPosID = Shader.PropertyToID("_TerrainWorldPos");
        terrainNormalID = Shader.PropertyToID("_TerrainNormal");
        randomSeedID = Shader.PropertyToID("_RandomSeed");
        
        propertyBlock = new MaterialPropertyBlock();
    }
    
    void GenerateGrassInstances()
    {
        if (terrain == null || grassMaterial == null || grassMesh == null)
        {
            Debug.LogError("Missing required components for grass generation!");
            return;
        }

        // Check if material supports instancing
        if (!grassMaterial.enableInstancing)
        {
            Debug.LogError("Grass material must have 'Enable GPU Instancing' checked in the material inspector!");
            return;
        }

        TerrainData terrainData = terrain.terrainData;
        Vector3 terrainPosition = terrain.transform.position;
        Vector3 terrainSize = terrainData.size;

        // Calculate grid dimensions based on terrain size and density
        int gridWidth = Mathf.RoundToInt(terrainSize.x * grassDensity);
        int gridLength = Mathf.RoundToInt(terrainSize.z * grassDensity);

        // Calculate step size for grid
        float stepX = terrainSize.x / gridWidth;
        float stepZ = terrainSize.z / gridLength;

        // Create temporary lists to store valid grass positions
        List<Matrix4x4> tempMatrices = new List<Matrix4x4>();
        List<Vector4> tempTerrainPos = new List<Vector4>();
        List<Vector4> tempTerrainNormals = new List<Vector4>();
        List<float> tempRandomSeeds = new List<float>();

        // Generate grass on grid with popout variations
        for (int x = 0; x < gridWidth; x++)
        {
            for (int z = 0; z < gridLength; z++)
            {
                // Check if this position should have grass based on popout rate
                if (Random.Range(0f, 1f) > popoutRate)
                    continue;

                // Calculate base grid position (0-1 normalized)
                float baseX = (float)x / gridWidth;
                float baseZ = (float)z / gridLength;

                // Add random popout offset
                float offsetX = Random.Range(-popoutDistance, popoutDistance) / terrainSize.x;
                float offsetZ = Random.Range(-popoutDistance, popoutDistance) / terrainSize.z;

                // Final normalized position with bounds checking
                float finalX = Mathf.Clamp01(baseX + offsetX);
                float finalZ = Mathf.Clamp01(baseZ + offsetZ);

                // Get terrain height and normal at this position
                float height = terrainData.GetInterpolatedHeight(finalX, finalZ);
                Vector3 normal = terrainData.GetInterpolatedNormal(finalX, finalZ);

                // Convert to world space
                Vector3 worldPos = new Vector3(
                    terrainPosition.x + finalX * terrainSize.x,
                    terrainPosition.y + height + yOffset,
                    terrainPosition.z + finalZ * terrainSize.z
                );

                // Store terrain data for shader
                tempTerrainPos.Add(new Vector4(worldPos.x, worldPos.y, worldPos.z, 0));
                tempTerrainNormals.Add(new Vector4(normal.x, normal.y, normal.z, 0));
                tempRandomSeeds.Add(Random.Range(0f, 1f));

                // Create grass transform matrix
                float scale = Random.Range(minScale, maxScale);
                float rotation = Random.Range(0f, 360f);

                Vector3 grassPosition = worldPos;
                Quaternion grassRotation = Quaternion.Euler(0, rotation, 0);
                Vector3 grassScale = Vector3.one * scale;

                tempMatrices.Add(Matrix4x4.TRS(grassPosition, grassRotation, grassScale));
            }
        }

        // Convert lists to arrays
        totalGrassCount = tempMatrices.Count;
        grassMatrices = tempMatrices.ToArray();
        terrainWorldPositions = tempTerrainPos.ToArray();
        terrainNormals = tempTerrainNormals.ToArray();
        randomSeeds = tempRandomSeeds.ToArray();

        Debug.Log($"Generated {totalGrassCount} grass instances on {gridWidth}x{gridLength} grid " +
                  $"(terrain size: {terrainSize.x}x{terrainSize.z}, density: {grassDensity})");
    }
    
    void Update()
    {
        if (grassMatrices == null || grassMaterial == null || grassMesh == null)
            return;
            
        RenderGrass();
    }
    
    void RenderGrass()
    {
        // Render grass instances in batches (Unity has a limit of 1023 instances per batch)
        int batchSize = 1023;
        int batches = Mathf.CeilToInt((float)totalGrassCount / batchSize);

        for (int batch = 0; batch < batches; batch++)
        {
            int startIndex = batch * batchSize;
            int count = Mathf.Min(batchSize, totalGrassCount - startIndex);

            // Create batch arrays
            Matrix4x4[] batchMatrices = new Matrix4x4[count];
            Vector4[] batchTerrainPos = new Vector4[count];
            Vector4[] batchTerrainNormals = new Vector4[count];
            float[] batchRandomSeeds = new float[count];

            System.Array.Copy(grassMatrices, startIndex, batchMatrices, 0, count);
            System.Array.Copy(terrainWorldPositions, startIndex, batchTerrainPos, 0, count);
            System.Array.Copy(terrainNormals, startIndex, batchTerrainNormals, 0, count);
            System.Array.Copy(randomSeeds, startIndex, batchRandomSeeds, 0, count);

            // Set batch-specific properties
            propertyBlock.SetVectorArray(terrainWorldPosID, batchTerrainPos);
            propertyBlock.SetVectorArray(terrainNormalID, batchTerrainNormals);
            propertyBlock.SetFloatArray(randomSeedID, batchRandomSeeds);

            // Render batch
            Graphics.DrawMeshInstanced(
                grassMesh,
                0,
                grassMaterial,
                batchMatrices,
                count,
                propertyBlock
            );
        }
    }
    
    void OnValidate()
    {
        // Regenerate grass when parameters change in editor
        if (Application.isPlaying && grassMatrices != null)
        {
            GenerateGrassInstances();
        }
    }
}
