Shader "Custom/Terrain"
{
    Properties
    {
        // Terrain textures
        [HideInInspector] _Control ("Control (RGBA)", 2D) = "red" {}
        [HideInInspector] _Splat3 ("Layer 3 (A)", 2D) = "white" {}
        [HideInInspector] _Splat2 ("Layer 2 (B)", 2D) = "white" {}
        [HideInInspector] _Splat1 ("Layer 1 (G)", 2D) = "white" {}
        [HideInInspector] _Splat0 ("Layer 0 (R)", 2D) = "white" {}

        // Base color for stylized terrain
        _BaseColor ("Base Color", Color) = (0.5, 0.7, 0.3, 1)

        // Toon shading properties
        _ShadowColor ("Shadow Color", Color) = (0.3, 0.4, 0.2, 1)
        _HighlightColor ("Highlight Color", Color) = (0.8, 1.0, 0.7, 1)
        _ShadowThreshold ("Shadow Threshold", Range(0, 1)) = 0.4
        _ShadowSoftness ("Shadow Softness", Range(0, 1)) = 0.1

        // Color zones for organic variation
        _ColorZones ("Color Zones", Range(2, 8)) = 4
        _NoiseScale ("Noise Scale", Range(0.1, 10)) = 2.0
        _ColorVariation ("Color Variation", Range(0, 1)) = 0.4

        // Terrain specific
        [HideInInspector] _TerrainHolesTexture("Holes Map (RGB)", 2D) = "white" {}
    }

    SubShader
    {
        Tags
        {
            "Queue" = "Geometry-100"
            "RenderType" = "Opaque"
            "TerrainCompatible" = "True"
        }
        LOD 200

        Pass
        {
            Name "TerrainToon"
            Tags { "LightMode" = "UniversalForward" }

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_instancing
            #pragma target 3.0

            // URP includes
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"

            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float2 texcoord : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv : TEXCOORD0;
                float3 positionWS : TEXCOORD1;
                float3 normalWS : TEXCOORD2;
                UNITY_VERTEX_OUTPUT_STEREO
            };

            // Terrain textures
            TEXTURE2D(_Control);
            SAMPLER(sampler_Control);
            TEXTURE2D(_Splat0);
            SAMPLER(sampler_Splat0);
            TEXTURE2D(_Splat1);
            SAMPLER(sampler_Splat1);
            TEXTURE2D(_Splat2);
            SAMPLER(sampler_Splat2);
            TEXTURE2D(_Splat3);
            SAMPLER(sampler_Splat3);

            CBUFFER_START(UnityPerMaterial)
                float4 _Control_ST;
                float4 _Splat0_ST;
                float4 _Splat1_ST;
                float4 _Splat2_ST;
                float4 _Splat3_ST;
                float4 _BaseColor;
                float4 _ShadowColor;
                float4 _HighlightColor;
                float _ShadowThreshold;
                float _ShadowSoftness;
                float _ColorZones;
                float _NoiseScale;
                float _ColorVariation;
            CBUFFER_END

            // Simple noise function for organic variation
            float SimpleNoise(float2 uv)
            {
                return frac(sin(dot(uv, float2(12.9898, 78.233))) * 43758.5453);
            }

            // Smooth noise for organic patterns
            float SmoothNoise(float2 uv)
            {
                float2 i = floor(uv);
                float2 f = frac(uv);
                f = f * f * (3.0 - 2.0 * f); // Smoothstep

                float a = SimpleNoise(i);
                float b = SimpleNoise(i + float2(1.0, 0.0));
                float c = SimpleNoise(i + float2(0.0, 1.0));
                float d = SimpleNoise(i + float2(1.0, 1.0));

                return lerp(lerp(a, b, f.x), lerp(c, d, f.x), f.y);
            }

            // Posterize function for flat color zones
            float3 PosterizeColor(float3 color, float zones)
            {
                return floor(color * zones) / zones;
            }

            Varyings vert(Attributes input)
            {
                Varyings output = (Varyings)0;

                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);

                VertexPositionInputs vertexInput = GetVertexPositionInputs(input.positionOS.xyz);
                VertexNormalInputs normalInput = GetVertexNormalInputs(input.normalOS);

                output.positionCS = vertexInput.positionCS;
                output.positionWS = vertexInput.positionWS;
                output.normalWS = normalInput.normalWS;
                output.uv = input.texcoord;

                return output;
            }

            half4 frag(Varyings input) : SV_Target
            {
                // Generate organic noise pattern for color variation
                float2 noiseUV = input.positionWS.xz * _NoiseScale;
                float noise1 = SmoothNoise(noiseUV);
                float noise2 = SmoothNoise(noiseUV * 2.3 + float2(1.7, 9.2));
                float noise3 = SmoothNoise(noiseUV * 0.5 + float2(8.3, 2.8));

                // Combine noise layers for organic variation
                float combinedNoise = (noise1 * 0.5 + noise2 * 0.3 + noise3 * 0.2);

                // Create color zones based on noise
                float colorZone = floor(combinedNoise * _ColorZones) / _ColorZones;

                // Start with base color and vary it based on zones
                half3 albedo = _BaseColor.rgb;

                // Create organic color variations like in the reference image
                float3 darkGreen = _BaseColor.rgb * 0.6;   // Dark forest green
                float3 mediumGreen = _BaseColor.rgb * 0.8; // Medium green
                float3 lightGreen = _BaseColor.rgb * 1.2;  // Light green
                float3 brightGreen = _BaseColor.rgb * 1.4; // Bright green

                // Assign colors based on noise zones for organic patches
                if (colorZone < 0.25)
                    albedo = darkGreen;
                else if (colorZone < 0.5)
                    albedo = mediumGreen;
                else if (colorZone < 0.75)
                    albedo = lightGreen;
                else
                    albedo = brightGreen;

                // Sample control texture for terrain blending (if using terrain textures)
                half4 splat_control = SAMPLE_TEXTURE2D(_Control, sampler_Control, input.uv);

                // Check if we have any terrain texture data
                float totalWeight = splat_control.r + splat_control.g + splat_control.b + splat_control.a;

                if (totalWeight > 0.01)
                {
                    // Sample terrain textures
                    half3 lay1 = SAMPLE_TEXTURE2D(_Splat0, sampler_Splat0, input.uv * _Splat0_ST.xy).rgb;
                    half3 lay2 = SAMPLE_TEXTURE2D(_Splat1, sampler_Splat1, input.uv * _Splat1_ST.xy).rgb;
                    half3 lay3 = SAMPLE_TEXTURE2D(_Splat2, sampler_Splat2, input.uv * _Splat2_ST.xy).rgb;
                    half3 lay4 = SAMPLE_TEXTURE2D(_Splat3, sampler_Splat3, input.uv * _Splat3_ST.xy).rgb;

                    // Blend terrain textures with the organic base
                    half3 terrainColor = lay1 * splat_control.r + lay2 * splat_control.g +
                                        lay3 * splat_control.b + lay4 * splat_control.a;

                    // Mix terrain textures with organic base color
                    albedo = lerp(albedo, terrainColor * albedo, totalWeight * 0.5);
                }

                // Get main light for toon shading
                Light mainLight = GetMainLight();
                float3 normalWS = normalize(input.normalWS);
                float NdotL = dot(normalWS, mainLight.direction);

                // Create flat toon lighting with hard boundaries
                float lightStep = step(_ShadowThreshold, NdotL);

                // Smooth the transition slightly if softness is enabled
                if (_ShadowSoftness > 0)
                {
                    lightStep = smoothstep(_ShadowThreshold - _ShadowSoftness,
                                          _ShadowThreshold + _ShadowSoftness, NdotL);
                }

                // Apply flat lighting - either full light or shadow
                half3 lightColor = lerp(_ShadowColor.rgb, _HighlightColor.rgb, lightStep);

                // Combine with albedo
                half3 baseColor = albedo * lightColor;

                // Posterize for flat color zones like in the reference image
                half3 finalColor = PosterizeColor(baseColor, _ColorZones);

                return half4(finalColor, 1.0);
            }
            ENDHLSL
        }
    }

    FallBack "Hidden/Universal Render Pipeline/FallbackError"
}
