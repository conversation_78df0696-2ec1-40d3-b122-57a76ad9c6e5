Shader "Custom/ToonTerrain"
{
    Properties
    {
        // Terrain textures
        [HideInInspector] _Control ("Control (RGBA)", 2D) = "red" {}
        [HideInInspector] _Splat3 ("Layer 3 (A)", 2D) = "grey" {}
        [HideInInspector] _Splat2 ("Layer 2 (B)", 2D) = "grey" {}
        [HideInInspector] _Splat1 ("Layer 1 (G)", 2D) = "grey" {}
        [HideInInspector] _Splat0 ("Layer 0 (R)", 2D) = "grey" {}
        
        // Toon shading properties
        _ToonRamp ("Toon Ramp", 2D) = "white" {}
        _ShadowColor ("Shadow Color", Color) = (0.3, 0.3, 0.3, 1)
        _HighlightColor ("Highlight Color", Color) = (1, 1, 1, 1)
        _ShadowThreshold ("Shadow Threshold", Range(0, 1)) = 0.4
        _ShadowSoftness ("Shadow Softness", Range(0, 1)) = 0.1
        
        // Color zones for consistent coloring
        _ColorZones ("Color Zones", Range(2, 12)) = 6
        _ColorPosterize ("Color Posterize", Range(0, 1)) = 1.0
        _FlatShading ("Flat Shading", Range(0, 1)) = 1.0
        
        // Terrain specific
        [HideInInspector] _TerrainHolesTexture("Holes Map (RGB)", 2D) = "white" {}
    }

    SubShader
    {
        Tags 
        { 
            "Queue" = "Geometry-100" 
            "RenderType" = "Opaque" 
            "TerrainCompatible" = "True"
        }
        LOD 200

        Pass
        {
            Name "TerrainToon"
            Tags { "LightMode" = "UniversalForward" }

            HLSLPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #pragma multi_compile_instancing
            #pragma target 3.0

            // URP includes
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
            #include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Lighting.hlsl"

            struct Attributes
            {
                float4 positionOS : POSITION;
                float3 normalOS : NORMAL;
                float2 texcoord : TEXCOORD0;
                UNITY_VERTEX_INPUT_INSTANCE_ID
            };

            struct Varyings
            {
                float4 positionCS : SV_POSITION;
                float2 uv : TEXCOORD0;
                float3 positionWS : TEXCOORD1;
                float3 normalWS : TEXCOORD2;
                UNITY_VERTEX_OUTPUT_STEREO
            };

            // Terrain textures
            TEXTURE2D(_Control);
            SAMPLER(sampler_Control);
            TEXTURE2D(_Splat0);
            SAMPLER(sampler_Splat0);
            TEXTURE2D(_Splat1);
            SAMPLER(sampler_Splat1);
            TEXTURE2D(_Splat2);
            SAMPLER(sampler_Splat2);
            TEXTURE2D(_Splat3);
            SAMPLER(sampler_Splat3);
            
            // Toon properties
            TEXTURE2D(_ToonRamp);
            SAMPLER(sampler_ToonRamp);
            
            CBUFFER_START(UnityPerMaterial)
                float4 _Control_ST;
                float4 _Splat0_ST;
                float4 _Splat1_ST;
                float4 _Splat2_ST;
                float4 _Splat3_ST;
                float4 _ShadowColor;
                float4 _HighlightColor;
                float _ShadowThreshold;
                float _ShadowSoftness;
                float _ColorZones;
                float _ColorPosterize;
                float _FlatShading;
            CBUFFER_END

            // Posterize function for toon effect
            float3 PosterizeColor(float3 color, float zones)
            {
                return floor(color * zones) / zones;
            }

            Varyings vert(Attributes input)
            {
                Varyings output = (Varyings)0;
                
                UNITY_SETUP_INSTANCE_ID(input);
                UNITY_INITIALIZE_VERTEX_OUTPUT_STEREO(output);

                VertexPositionInputs vertexInput = GetVertexPositionInputs(input.positionOS.xyz);
                VertexNormalInputs normalInput = GetVertexNormalInputs(input.normalOS);

                output.positionCS = vertexInput.positionCS;
                output.positionWS = vertexInput.positionWS;
                output.normalWS = normalInput.normalWS;
                output.uv = TRANSFORM_TEX(input.texcoord, _Control);

                return output;
            }

            half4 frag(Varyings input) : SV_Target
            {
                // Sample control texture for terrain blending
                half4 splat_control = SAMPLE_TEXTURE2D(_Control, sampler_Control, input.uv);

                // Sample terrain textures
                half3 lay1 = SAMPLE_TEXTURE2D(_Splat0, sampler_Splat0, input.uv * _Splat0_ST.xy).rgb;
                half3 lay2 = SAMPLE_TEXTURE2D(_Splat1, sampler_Splat1, input.uv * _Splat1_ST.xy).rgb;
                half3 lay3 = SAMPLE_TEXTURE2D(_Splat2, sampler_Splat2, input.uv * _Splat2_ST.xy).rgb;
                half3 lay4 = SAMPLE_TEXTURE2D(_Splat3, sampler_Splat3, input.uv * _Splat3_ST.xy).rgb;

                // Blend terrain textures
                half3 albedo = lay1 * splat_control.r + lay2 * splat_control.g +
                              lay3 * splat_control.b + lay4 * splat_control.a;

                // Get main light for basic lighting direction
                Light mainLight = GetMainLight();
                float3 normalWS = normalize(input.normalWS);
                float NdotL = dot(normalWS, mainLight.direction);

                // Create flat toon lighting with hard boundaries
                float lightStep = step(_ShadowThreshold, NdotL);

                // Apply flat lighting - either full light or shadow, no gradients
                half3 lightColor = lerp(_ShadowColor.rgb, _HighlightColor.rgb, lightStep);

                // Combine with albedo
                half3 baseColor = albedo * lightColor;

                // Strong posterization for flat color zones like in the image
                half3 finalColor = PosterizeColor(baseColor, _ColorZones);

                // Additional posterization pass for even flatter look
                finalColor = PosterizeColor(finalColor, _ColorZones * 0.7);

                return half4(finalColor, 1.0);
            }
            ENDHLSL
        }
    }
    
    FallBack "Hidden/Universal Render Pipeline/FallbackError"
}
